# 分布式流媒体架构设计文档

## 1. 系统架构概述

### 1.1 服务器角色分配
- **主服务器（1台）**：业务逻辑、数据库、按需拉流、用户访问入口
- **次服务器（8台）**：边缘录制、本地存储、流媒体服务

### 1.2 架构优势
- **带宽优化**：只有观看时才拉流，大幅减少网络传输
- **就近录制**：摄像机通过次服务器本地处理，减少网络延迟
- **负载分散**：8台服务器分担录制和存储压力
- **容错能力**：单台服务器故障不影响其他区域
- **成本优化**：无需额外推流设备，次服务器直接处理摄像机流

## 2. 技术架构设计

### 2.1 网络拓扑（推荐方案）
```
摄像机(RTSP) → 次服务器(FFmpeg推流脚本) → 次服务器(SRS) → 本地录制存储
                                                    ↓
主服务器 ← 按需拉流 ← 用户观看请求
```

### 2.2 备选网络拓扑
```
摄像机(RTSP) → 推流电脑(FFmpeg) → 次服务器(SRS) → 本地录制存储
                                            ↓
主服务器 ← 按需拉流 ← 用户观看请求
```

### 2.3 数据流向详解

#### 推荐方案的数据流向
1. **摄像机流**：摄像机(RTSP) → 次服务器FFmpeg脚本
2. **推流处理**：次服务器FFmpeg → 次服务器SRS(RTMP)
3. **本地录制**：次服务器SRS → 本地存储(MP4文件)
4. **按需拉流**：主服务器SRS ← 次服务器SRS(用户观看时)
5. **用户播放**：用户 ← 主服务器SRS(HTTP-FLV/HLS)
6. **元数据管理**：所有业务数据存储在主服务器数据库

#### 备选方案的数据流向
1. **摄像机流**：摄像机(RTSP) → 推流电脑FFmpeg
2. **网络推流**：推流电脑FFmpeg → 次服务器SRS(RTMP)
3. **本地录制**：次服务器SRS → 本地存储(MP4文件)
4. **按需拉流**：主服务器SRS ← 次服务器SRS(用户观看时)
5. **用户播放**：用户 ← 主服务器SRS(HTTP-FLV/HLS)

## 3. 核心功能模块

### 3.1 设备路由模块
**功能**：根据设备编号确定对应的次服务器

**实现方式**：
```java
@Service
public class DeviceRoutingService {
    // 设备编号到服务器的映射关系
    private Map<String, String> deviceServerMapping;
    
    public String getEdgeServerByDevice(String equipmentNumber) {
        // 根据设备编号或地理位置路由到对应次服务器
        return deviceServerMapping.get(equipmentNumber);
    }
}
```

**路由策略**：
- 按地理位置分组：每台次服务器负责特定区域的设备
- 负载均衡：可根据服务器负载动态调整
- 容错机制：主服务器故障时自动切换到备用服务器

### 3.2 按需拉流模块
**功能**：用户观看时自动从次服务器拉取视频流

**核心流程**：
1. 用户请求观看特定摄像机
2. 检查主服务器是否已有该流
3. 如果没有，启动拉流进程
4. 监控流状态，就绪后开始播放
5. 无观看者时自动停止拉流

**技术实现**：
```java
@Service
public class StreamPullService {
    
    public SaResult startPullStream(String streamName) {
        // 1. 获取对应的次服务器
        String edgeServer = deviceRoutingService.getEdgeServerByDevice(streamName);
        
        // 2. 构建拉流URL
        String pullUrl = "rtmp://" + edgeServer + ":1935/live/" + streamName;
        
        // 3. 启动SRS Ingest
        return srsIngestManager.startIngest(streamName, pullUrl);
    }
    
    @Scheduled(fixedRate = 60000)
    public void checkAndStopIdleStreams() {
        // 定时检查无观看者的流并停止
    }
}
```

### 3.3 流状态管理模块
**功能**：跟踪和管理所有活跃的视频流

**状态类型**：
- INACTIVE：流未启动
- PULLING：正在拉流中
- ACTIVE：流已就绪可播放
- ERROR：拉流失败

**实现方式**：
```java
@Component
public class StreamStateManager {
    private Map<String, StreamInfo> activeStreams = new ConcurrentHashMap<>();
    
    public void updateStreamState(String streamName, StreamState state) {
        // 更新流状态并通知相关组件
    }
    
    public boolean isStreamActive(String streamName) {
        // 检查流是否处于活跃状态
    }
}
```

## 4. 摄像机推流方案详解

### 4.1 推荐方案：次服务器直接推流

#### 4.1.1 架构优势
- **成本最低**：无需额外硬件设备
- **延迟最小**：摄像机到SRS在同一台服务器，减少网络传输
- **管理简单**：一台服务器完成推流、录制、存储
- **稳定性高**：减少网络传输环节，降低故障点

#### 4.1.2 次服务器推流脚本
```bash
#!/bin/bash
# /home/<USER>/camera-push.sh
# 次服务器推流脚本

# 摄像机配置
CAMERA_IP="*************"
CAMERA_USER="admin"
CAMERA_PASS="password"
CAMERA_RTSP="rtsp://${CAMERA_USER}:${CAMERA_PASS}@${CAMERA_IP}:554/stream1"

# 本地SRS配置
LOCAL_SRS="rtmp://127.0.0.1:1935/live/c0742bfc29f11921680120"

# 启动推流
ffmpeg -i $CAMERA_RTSP \
       -c:v copy -c:a copy \
       -f flv $LOCAL_SRS \
       -loglevel error \
       -reconnect 1 \
       -reconnect_streamed 1 \
       -reconnect_delay_max 2
```

#### 4.1.3 自动启动配置
```bash
# /etc/systemd/system/camera-push.service
[Unit]
Description=Camera Push Service
After=network.target

[Service]
Type=simple
User=root
ExecStart=/home/<USER>/camera-push.sh
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
```

### 4.2 备选方案：独立推流电脑

#### 4.2.1 适用场景
- 次服务器性能有限
- 需要处理多个摄像机
- 要求推流和录制完全分离

#### 4.2.2 推流电脑配置
```bash
# 推流电脑脚本
#!/bin/bash
CAMERA_RTSP="rtsp://admin:password@*************:554/stream1"
EDGE_SERVER="rtmp://*************:1935/live/c0742bfc29f11921680120"

ffmpeg -i $CAMERA_RTSP \
       -c:v copy -c:a copy \
       -f flv $EDGE_SERVER
```

## 5. 部署配置

### 5.1 次服务器SRS配置
```conf
# /etc/srs/srs.conf
listen 1935;
max_connections 1000;

http_server {
    enabled on;
    listen 8080;
    dir ./objs/nginx/html;
}

vhost __defaultVhost__ {
    # 启用录制
    dvr {
        enabled on;
        dvr_path /home/<USER>/live/[app]/[stream]/[2006]/[01]/[02]/[15].[04].[05].[999].mp4;
        dvr_plan session;
    }
    
    # 允许播放（供主服务器拉流）
    play {
        enabled on;
    }
    
    # 允许推流（摄像机推流）
    publish {
        enabled on;
    }
}
```

### 4.2 用户观看完整流程示例

#### 4.2.1 用户观看次服务器5摄像机的详细流程

**步骤1：用户发起观看请求**
```
用户访问：http://主服务器:8080/stream/watch/c0742bfc29f11921680120
```

**步骤2：主服务器处理请求**
```java
@GetMapping("/stream/watch/{streamName}")
public String watchStream(@PathVariable String streamName) {
    // 2.1 根据设备编号确定次服务器
    String edgeServer = deviceRoutingService.getEdgeServerByDevice(streamName);
    // 结果：edgeServer = "*************" (次服务器5)

    // 2.2 检查主服务器是否已有该流
    if (!streamStateManager.isStreamActive(streamName)) {
        // 2.3 启动拉流
        streamPullService.startPullStream(streamName);
    }

    // 2.4 返回播放页面
    return "live-player";
}
```

**步骤3：主服务器启动拉流**
```java
public SaResult startPullStream(String streamName) {
    // 3.1 构建次服务器5的拉流地址
    String pullUrl = "rtmp://*************:1935/live/c0742bfc29f11921680120";

    // 3.2 调用SRS API启动拉流
    Map<String, Object> ingestConfig = Map.of(
        "ingest_id", streamName,
        "input", Map.of("url", pullUrl),
        "output", "rtmp://127.0.0.1:1935/live/" + streamName
    );

    // 3.3 发送到主服务器SRS
    restTemplate.postForObject("http://localhost:1985/api/v1/ingests/", ingestConfig);
}
```

**步骤4：数据流转过程**
```
次服务器5：摄像机(RTSP) → FFmpeg脚本 → SRS(rtmp://127.0.0.1:1935/live/stream)
                                              ↓
主服务器：SRS拉取(rtmp://*************:1935/live/stream)
                                              ↓
用户播放：http://主服务器:8080/live/stream.flv
```

**步骤5：用户开始播放**
```javascript
// 前端播放器自动播放主服务器的流
videoElement.src = "http://主服务器:8080/live/c0742bfc29f11921680120.flv";
```

### 4.3 主服务器SRS配置
```conf
# 主服务器SRS配置
listen 1935;
max_connections 2000;

http_server {
    enabled on;
    listen 8080;
}

http_api {
    enabled on;
    listen 1985;
}

vhost __defaultVhost__ {
    # 只提供播放服务
    play {
        enabled on;
    }
    
    # 禁用录制（由次服务器负责）
    dvr {
        enabled off;
    }
}
```

### 4.3 应用配置
```yaml
# application.yml
stream:
  edge-servers:
    server-1: "*************"
    server-2: "*************"
    server-3: "*************"
    # ... 其他服务器
  
  device-mapping:
    # 设备编号到服务器的映射
    "c0742bfc29f1": "server-1"
    "c0742bfc29f2": "server-1"
    "c0742bfc29f3": "server-2"
    # ...
```

## 5. API接口设计

### 5.1 流媒体控制接口
```java
@RestController
@RequestMapping("/stream")
public class StreamController {
    
    // 开始观看（自动拉流）
    @GetMapping("/watch/{streamName}")
    public String watchStream(@PathVariable String streamName) {
        streamPullService.ensureStreamActive(streamName);
        return "live-player";
    }
    
    // 手动启动拉流
    @PostMapping("/pull/{streamName}")
    public SaResult startPull(@PathVariable String streamName) {
        return streamPullService.startPullStream(streamName);
    }
    
    // 停止拉流
    @DeleteMapping("/pull/{streamName}")
    public SaResult stopPull(@PathVariable String streamName) {
        return streamPullService.stopPullStream(streamName);
    }
    
    // 获取流状态
    @GetMapping("/status/{streamName}")
    public SaResult getStreamStatus(@PathVariable String streamName) {
        return SaResult.data(streamStateManager.getStreamInfo(streamName));
    }
}
```

### 5.2 设备管理接口
```java
@RestController
@RequestMapping("/device")
public class DeviceController {
    
    // 获取设备对应的服务器
    @GetMapping("/server/{equipmentNumber}")
    public SaResult getDeviceServer(@PathVariable String equipmentNumber) {
        String server = deviceRoutingService.getEdgeServerByDevice(equipmentNumber);
        return SaResult.data(server);
    }
    
    // 更新设备服务器映射
    @PostMapping("/mapping")
    public SaResult updateDeviceMapping(@RequestBody Map<String, String> mapping) {
        deviceRoutingService.updateMapping(mapping);
        return SaResult.ok("映射更新成功");
    }
}
```

## 6. 监控和运维

### 6.1 健康检查
- 定时检查各次服务器SRS服务状态
- 监控拉流进程状态
- 检查录制文件生成情况

### 6.2 日志记录
- 拉流启动/停止日志
- 流状态变化日志
- 错误和异常日志

### 6.3 性能监控
- 带宽使用情况
- 并发流数量
- 服务器资源使用率

## 7. 扩展性考虑

### 7.1 水平扩展
- 可根据需要增加更多次服务器
- 支持动态调整设备到服务器的映射关系

### 7.2 负载均衡
- 可在次服务器前增加负载均衡器
- 支持基于地理位置的智能路由

### 7.3 容灾备份
- 关键数据定期备份
- 支持服务器故障时的自动切换

## 8. 实施难度评估

### 8.1 难度等级：中等偏易

#### 8.1.1 为什么不难
- **技术栈熟悉**：基于现有SRS、Java Spring Boot、FFmpeg
- **渐进式实施**：可以先测试1台次服务器，再扩展
- **代码量适中**：核心功能代码量不大

#### 8.1.2 主要工作量
- **服务器部署**：1-2天（SRS安装、网络配置、推流脚本）
- **代码开发**：2-3天（设备路由、拉流控制、前端适配）
- **测试调试**：1-2天（单台测试、多台联调、性能测试）

### 8.2 分阶段实施计划

#### 第一阶段：单台次服务器验证（1-2天）
1. 选择1台次服务器部署SRS
2. 配置1个摄像机推流到次服务器
3. 实现主服务器拉流功能
4. 验证用户观看流程

#### 第二阶段：核心功能开发（2-3天）
1. 开发设备路由服务
2. 实现流状态管理
3. 完成按需拉流逻辑
4. 前端播放页面适配

#### 第三阶段：多服务器扩展（1-2天）
1. 部署剩余7台次服务器
2. 配置设备到服务器的映射关系
3. 批量部署推流脚本
4. 全系统联调测试

#### 第四阶段：优化和监控（1-2天）
1. 添加性能监控
2. 优化带宽使用策略
3. 完善容错和重连机制
4. 压力测试和性能调优

### 8.3 关键技术挑战

#### 8.3.1 网络配置
- **挑战**：确保主服务器能访问所有次服务器
- **解决**：配置防火墙开放1935、8080端口，测试网络连通性

#### 8.3.2 推流脚本稳定性
- **挑战**：摄像机断线重连、FFmpeg进程监控
- **解决**：使用systemd管理服务，添加自动重启机制

#### 8.3.3 并发拉流管理
- **挑战**：多用户同时观看时的流管理
- **解决**：实现流引用计数，最后一个用户离开时停止拉流

### 8.4 预期效果

#### 8.4.1 带宽优化效果
- **平时带宽消耗**：接近0（只有局域网推流）
- **观看时带宽**：按需消耗，比现在减少80%以上
- **存储分布**：8台服务器分担，单台压力减少87.5%

#### 8.4.2 系统可靠性提升
- **单点故障影响**：从100%降低到12.5%（1/8）
- **录制稳定性**：本地录制，网络故障不影响录制
- **扩展能力**：可以轻松增加更多次服务器

#### 8.4.3 用户体验保持
- **观看体验**：与现在完全一致
- **功能完整性**：所有现有功能都保持
- **响应速度**：拉流延迟2-3秒，基本无感知

## 9. 分布式文件存储架构

### 9.1 MinIO分布式存储方案

#### 9.1.1 架构设计
```
主服务器
├── Spring Boot应用 (业务逻辑)
├── MySQL数据库 (业务数据 + 文件元数据)
└── MinIO Gateway (统一文件入口)

次服务器1-8
├── MinIO Server (文件存储节点)
├── SRS流媒体服务
└── 本地磁盘存储
```

#### 9.1.2 核心理念
- **计算集中**：所有HTTP请求和业务逻辑在主服务器处理
- **存储分布**：图片、视频、文档等文件分布存储到次服务器
- **统一入口**：用户只访问主服务器，MinIO自动路由到正确的存储节点

### 9.2 技术实现

#### 9.2.1 MinIO集群配置
```yaml
# 主服务器application.yml
minio:
  gateway:
    enabled: true
    access-key: "admin"
    secret-key: "password"
    clusters:
      region-north:
        endpoints:
          - "http://*************:9000"  # 次服务器1
          - "http://*************:9000"  # 次服务器2
      region-south:
        endpoints:
          - "http://*************:9000"  # 次服务器3
          - "http://192.168.1.104:9000"  # 次服务器4
      region-east:
        endpoints:
          - "http://*************:9000"  # 次服务器5
          - "http://*************:9000"  # 次服务器6
      region-west:
        endpoints:
          - "http://*************:9000"  # 次服务器7
          - "http://*************:9000"  # 次服务器8
```

#### 9.2.2 分布式文件服务
```java
@Service
public class DistributedFileService {

    @Autowired
    private MinioClient minioClient;

    /**
     * 根据地理位置上传文件
     */
    public String uploadFile(MultipartFile file, String city, String county, String township) {
        // 根据地理位置选择MinIO集群
        String cluster = getClusterByLocation(city, county, township);

        // 生成文件名
        String fileName = generateFileName(file.getOriginalFilename());

        try {
            // 上传到指定集群
            minioClient.putObject(
                PutObjectArgs.builder()
                    .bucket("illegal-files")
                    .object(fileName)
                    .stream(file.getInputStream(), file.getSize(), -1)
                    .contentType(file.getContentType())
                    .extraHeaders(Map.of("X-Cluster", cluster))  // 指定集群
                    .build()
            );

            // 记录文件信息到数据库
            saveFileInfo(fileName, cluster, file.getSize(), file.getContentType());

            return fileName;

        } catch (Exception e) {
            log.error("文件上传失败", e);
            throw new RuntimeException("文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 下载文件
     */
    public InputStream downloadFile(String fileName) {
        try {
            return minioClient.getObject(
                GetObjectArgs.builder()
                    .bucket("illegal-files")
                    .object(fileName)
                    .build()
            );
        } catch (Exception e) {
            log.error("文件下载失败: {}", fileName, e);
            throw new RuntimeException("文件下载失败: " + e.getMessage());
        }
    }

    /**
     * 根据地理位置选择集群
     */
    private String getClusterByLocation(String city, String county, String township) {
        // 根据地理位置映射到集群
        if (city.contains("北") || county.contains("北")) {
            return "region-north";
        } else if (city.contains("南") || county.contains("南")) {
            return "region-south";
        } else if (city.contains("东") || county.contains("东")) {
            return "region-east";
        } else {
            return "region-west";
        }
    }
}
```

#### 9.2.3 文件访问控制器
```java
@RestController
@RequestMapping("/file")
public class FileController {

    @Autowired
    private DistributedFileService fileService;

    /**
     * 文件上传
     */
    @PostMapping("/upload")
    public SaResult uploadFile(
            @RequestParam("file") MultipartFile file,
            @RequestParam("city") String city,
            @RequestParam("county") String county,
            @RequestParam("township") String township) {

        try {
            String fileName = fileService.uploadFile(file, city, county, township);
            return SaResult.ok("文件上传成功").setData(fileName);
        } catch (Exception e) {
            return SaResult.error("文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 文件下载
     */
    @GetMapping("/download/{fileName}")
    public void downloadFile(@PathVariable String fileName, HttpServletResponse response) {
        try {
            InputStream fileStream = fileService.downloadFile(fileName);

            // 设置响应头
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment; filename=" + fileName);

            // 返回文件流
            IOUtils.copy(fileStream, response.getOutputStream());

        } catch (Exception e) {
            log.error("文件下载失败: {}", fileName, e);
            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
        }
    }

    /**
     * 获取文件访问URL
     */
    @GetMapping("/url/{fileName}")
    public SaResult getFileUrl(@PathVariable String fileName) {
        try {
            // 生成预签名URL（可选）
            String url = minioClient.getPresignedObjectUrl(
                GetPresignedObjectUrlArgs.builder()
                    .method(Method.GET)
                    .bucket("illegal-files")
                    .object(fileName)
                    .expiry(60 * 60)  // 1小时有效期
                    .build()
            );

            return SaResult.ok("获取文件URL成功").setData(url);
        } catch (Exception e) {
            return SaResult.error("获取文件URL失败: " + e.getMessage());
        }
    }
}
```

### 9.3 数据库设计

#### 9.3.1 文件信息表
```sql
-- 文件存储信息表（存储在主服务器MySQL）
CREATE TABLE file_storage (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    file_name VARCHAR(255) NOT NULL COMMENT '文件名',
    original_name VARCHAR(255) COMMENT '原始文件名',
    file_path VARCHAR(500) COMMENT '文件路径',
    storage_cluster VARCHAR(50) COMMENT '存储集群标识',
    file_size BIGINT COMMENT '文件大小（字节）',
    file_type VARCHAR(50) COMMENT '文件类型',
    content_type VARCHAR(100) COMMENT 'MIME类型',
    upload_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
    city VARCHAR(50) COMMENT '所属城市',
    county VARCHAR(50) COMMENT '所属县区',
    township VARCHAR(50) COMMENT '所属乡镇',
    created_by VARCHAR(50) COMMENT '创建人',
    status TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-删除',

    INDEX idx_file_name (file_name),
    INDEX idx_storage_cluster (storage_cluster),
    INDEX idx_location (city, county, township),
    INDEX idx_upload_time (upload_time)
) COMMENT '分布式文件存储信息表';
```

#### 9.3.2 违法记录表扩展
```sql
-- 为现有违法记录表添加文件字段
ALTER TABLE illegal_records
ADD COLUMN evidence_files JSON COMMENT '证据文件列表',
ADD COLUMN video_file VARCHAR(255) COMMENT '违法视频文件名',
ADD COLUMN image_files JSON COMMENT '违法图片文件列表';
```

### 9.4 MinIO服务器配置

#### 9.4.1 次服务器MinIO部署
```bash
# 次服务器MinIO启动脚本
#!/bin/bash
# /home/<USER>/start-minio.sh

export MINIO_ROOT_USER=admin
export MINIO_ROOT_PASSWORD=password123

# 启动MinIO服务器
minio server /home/<USER>/data \
    --address ":9000" \
    --console-address ":9001"
```

#### 9.4.2 MinIO系统服务配置
```ini
# /etc/systemd/system/minio.service
[Unit]
Description=MinIO Object Storage
After=network.target

[Service]
Type=simple
User=minio
Group=minio
Environment=MINIO_ROOT_USER=admin
Environment=MINIO_ROOT_PASSWORD=password123
ExecStart=/usr/local/bin/minio server /home/<USER>/data --address ":9000" --console-address ":9001"
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
```

### 9.5 MinIO方案优势

#### 9.5.1 技术优势
- **S3兼容API**：标准化接口，代码简单
- **自动负载均衡**：MinIO自动选择最优存储节点
- **地理位置感知**：支持基于位置的智能路由
- **强一致性**：保证数据完整性和一致性
- **自动故障转移**：节点故障时自动切换

#### 9.5.2 运维优势
- **统一管理**：通过MinIO Console统一管理所有节点
- **监控完善**：内置监控和告警功能
- **扩展简单**：可以轻松添加新的存储节点
- **备份恢复**：支持自动备份和数据恢复

#### 9.5.3 成本优势
- **开源免费**：无需额外的许可费用
- **硬件要求低**：可以运行在普通服务器上
- **带宽优化**：智能路由减少网络传输
- **存储效率高**：支持数据压缩和去重

## 10. 总结

这个基于SRS流媒体 + MinIO文件存储的分布式架构，实现了：

### 10.1 流媒体分布式
- **录制本地化**：视频录制到对应的次服务器
- **观看按需化**：用户观看时才从次服务器拉流
- **带宽最优化**：大幅减少网络传输，节省成本

### 10.2 文件存储分布式
- **存储分布化**：图片、文档等文件分布存储到次服务器
- **访问统一化**：用户只访问主服务器，MinIO自动路由
- **管理集中化**：文件元数据集中存储在主服务器数据库

### 10.3 整体架构优势
- **计算存储分离**：主服务器专注业务逻辑，次服务器专注存储
- **负载分散**：8台服务器分担存储和流媒体压力
- **容错能力强**：单台服务器故障影响范围小
- **扩展性好**：可以轻松增加更多次服务器

**总体评估：这是一个技术先进、架构合理、成本优化的分布式解决方案，预计2周内可以完成完整实施。**
