0000000000000000000000000000000000000000 bb7d6c28d390ccf896e3c739e1d2a81748a06326 郝华强 <<EMAIL>> 1746754870 +0800	clone: from https://gitee.com/hao-ot/countryside-java.git
bb7d6c28d390ccf896e3c739e1d2a81748a06326 ae12d84d23e1c9191093284d11479481d5060888 郝华强 <<EMAIL>> 1746755103 +0800	commit: 增加报警字段
ae12d84d23e1c9191093284d11479481d5060888 157d139ac99072484c573c65e4866968e1effaf8 郝华强 <<EMAIL>> 1747210241 +0800	commit: 增加部分
157d139ac99072484c573c65e4866968e1effaf8 df30321bd027c96b59f4f61bc7cbcbdf139f7ea6 郝华强 <<EMAIL>> 1747298284 +0800	commit: 修改脱岗逻辑
df30321bd027c96b59f4f61bc7cbcbdf139f7ea6 92efa4bd0d2e3c7d222d7a510222237f77e08a66 郝华强 <<EMAIL>> 1747390197 +0800	commit: 加入excel
92efa4bd0d2e3c7d222d7a510222237f77e08a66 945561780c4371350cf57e8470b77862fc99fa9e 郝华强 <<EMAIL>> 1747619813 +0800	pull --no-stat -v --progress origin master: Fast-forward
945561780c4371350cf57e8470b77862fc99fa9e b92a6b50e5cfb406a3e786154e53a90b785d4be5 郝华强 <<EMAIL>> 1747640269 +0800	commit: 补充考勤导出
b92a6b50e5cfb406a3e786154e53a90b785d4be5 de6a85ecc91f45f311efcf43671f2ba2f4fdd19c 郝华强 <<EMAIL>> 1747798495 +0800	commit: 暂定加入设备故障历史记录表
de6a85ecc91f45f311efcf43671f2ba2f4fdd19c 9bfaede151266f9ef34fb448e2a4a287b0b2b47f 郝华强 <<EMAIL>> 1747883520 +0800	commit: 地图点位修改
9bfaede151266f9ef34fb448e2a4a287b0b2b47f 9bfaede151266f9ef34fb448e2a4a287b0b2b47f 郝华强 <<EMAIL>> 1747901080 +0800	reset: moving to 9bfaede151266f9ef34fb448e2a4a287b0b2b47f
9bfaede151266f9ef34fb448e2a4a287b0b2b47f c433f803728a644bf3e35e526f97a6d7caa67d80 郝华强 <<EMAIL>> 1747903466 +0800	commit: 地图点位修改
c433f803728a644bf3e35e526f97a6d7caa67d80 c433f803728a644bf3e35e526f97a6d7caa67d80 郝华强 <<EMAIL>> 1747905640 +0800	reset: moving to c433f803728a644bf3e35e526f97a6d7caa67d80
c433f803728a644bf3e35e526f97a6d7caa67d80 79e7129c69aaee1dbbce589c4d35ab5c7d8a4a87 郝华强 <<EMAIL>> 1747994275 +0800	commit: 地图点位修改和考勤状态修改
79e7129c69aaee1dbbce589c4d35ab5c7d8a4a87 2606404eb9efada2a32e99020f40b6f9ce93393e 郝华强 <<EMAIL>> 1748329871 +0800	commit: 修改人员状态逻辑判断BUG
2606404eb9efada2a32e99020f40b6f9ce93393e 04d6d440ada54069d025106ee788c2f42d55ae5a 郝华强 <<EMAIL>> 1748333235 +0800	commit: 修改设备状态更改bug
04d6d440ada54069d025106ee788c2f42d55ae5a 2d73e405abf131e7d41062c176e38e7d8f11aa49 郝华强 <<EMAIL>> 1748339324 +0800	commit: 修改考勤统计导入导出
2d73e405abf131e7d41062c176e38e7d8f11aa49 0155ed8ab98cfb8e7de84a2b5a06137514c5eb1d 郝华强 <<EMAIL>> 1748511202 +0800	commit: 修改考勤状态返回全部人员信息和状态异常bug
0155ed8ab98cfb8e7de84a2b5a06137514c5eb1d 9707441a692ab4a587a7c84575ac3fd898236a70 郝华强 <<EMAIL>> 1749723077 +0800	commit: 修改精准劝导问题
9707441a692ab4a587a7c84575ac3fd898236a70 cbac443aaf3bc32500c889b5bd7c19afcad2d5f0 郝华强 <<EMAIL>> 1749786417 +0800	commit: 加入精准劝导每天凌晨2点执行，核销超过30天的精准劝导记录
cbac443aaf3bc32500c889b5bd7c19afcad2d5f0 e030da5d10036ff8a2768502712ae89c32530a6a 郝华强 <<EMAIL>> 1749808650 +0800	commit: 控制前端设备推流
e030da5d10036ff8a2768502712ae89c32530a6a 1cd1982ac8d27178a3678e97e7b31711602cd69b 郝华强 <<EMAIL>> 1750057546 +0800	commit: 控制前端设备推流以及对应脚本
1cd1982ac8d27178a3678e97e7b31711602cd69b e8fa2a7c71206084e1b7088cef5dcb5a3d8bc52b 郝华强 <<EMAIL>> 1750755368 +0800	commit: 修改精准劝导率统计，修改部分接口参数
e8fa2a7c71206084e1b7088cef5dcb5a3d8bc52b d3dcc942a47497cae71e1e46c78983431eece1c2 郝华强 <<EMAIL>> 1750814181 +0800	commit: 修改违法趋势接口参数，增加修改后车牌，修改加班计算方式
d3dcc942a47497cae71e1e46c78983431eece1c2 b83a9a6aa63fe1afdce3e29cb508cecef1703d20 郝华强 <<EMAIL>> 1750843720 +0800	commit: 处理劝导员在岗，未取消脱岗状态bug
b83a9a6aa63fe1afdce3e29cb508cecef1703d20 bb722969b020db44a3f8641915b19d19d3eefcc3 郝华强 <<EMAIL>> 1750906546 +0800	commit: 修改处理前一天打卡定时任务，处理延迟提交车牌重复提交逻辑
bb722969b020db44a3f8641915b19d19d3eefcc3 ad07be55b94122706924668a69eac6c841d8a85d 郝华强 <<EMAIL>> 1750919388 +0800	commit: 处理打印
ad07be55b94122706924668a69eac6c841d8a85d 3c4a24349f6c3a8885f493820897b41e8cbef47d 郝华强 <<EMAIL>> 1751354942 +0800	commit: 新增违法处理中的二次识别转发，修改新增大屏中获取点位详细数据接口
3c4a24349f6c3a8885f493820897b41e8cbef47d c6c39b66f2630c0cf96238c8bca4e5f2df79f1dc 郝华强 <<EMAIL>> 1751622738 +0800	commit: 修改违法处理中的二次识别bug，新增精准劝导接口，修改大屏部分页面接口传参
c6c39b66f2630c0cf96238c8bca4e5f2df79f1dc 1dbd398d45caca60f8bde1a6a39b27b62af0fef6 郝华强 <<EMAIL>> 1751937153 +0800	pull --no-stat -v --progress origin master: Fast-forward
1dbd398d45caca60f8bde1a6a39b27b62af0fef6 6f8feb13b743275b3b99ef587bf2b20af94aa10c 郝华强 <<EMAIL>> 1751966635 +0800	commit: 新增精准劝导接口，上岗情况统计时间，加入redis缓存预热
6f8feb13b743275b3b99ef587bf2b20af94aa10c c90bfc870f886fb8ccc089547a41c1bce2fbdde8 郝华强 <<EMAIL>> 1752460054 +0800	commit: 修改导出考勤数据背景色
c90bfc870f886fb8ccc089547a41c1bce2fbdde8 29f4b6e7e404b10bc883e25ae7c523d3c07f8f46 郝华强 <<EMAIL>> 1752644894 +0800	commit: 恢复原本违法页面暂时，处理二次识别保留车牌等相关逻辑
29f4b6e7e404b10bc883e25ae7c523d3c07f8f46 07c1b07fd7185cf4866f873bcef432debdccf90e 郝华强 <<EMAIL>> 1753326199 +0800	commit: 加入视频回放
07c1b07fd7185cf4866f873bcef432debdccf90e ff5bbba2939f18411442fe1cd1edbc8552c921cc 郝华强 <<EMAIL>> 1753348141 +0800	commit: 加入视频回放
ff5bbba2939f18411442fe1cd1edbc8552c921cc 9ba1b13b6a8facb77b928edfb1e623a5a8e05ebb 郝华强 <<EMAIL>> 1753408980 +0800	commit: 加入永久推流
9ba1b13b6a8facb77b928edfb1e623a5a8e05ebb 1578ec1ecb8fb30e2cfdcd620d62648f4cde4788 郝华强 <<EMAIL>> 1753932333 +0800	commit: 修复点位状态判断缺陷
1578ec1ecb8fb30e2cfdcd620d62648f4cde4788 59fcee7c761b3df13eac291e022b027d20457a50 郝华强 <<EMAIL>> 1753937308 +0800	commit: 修复勤务安排加载速度慢的问题
59fcee7c761b3df13eac291e022b027d20457a50 9ba1b13b6a8facb77b928edfb1e623a5a8e05ebb 郝华强 <<EMAIL>> 1753952265 +0800	reset: moving to 9ba1b13b6a8facb77b928edfb1e623a5a8e05ebb
9ba1b13b6a8facb77b928edfb1e623a5a8e05ebb 59fcee7c761b3df13eac291e022b027d20457a50 郝华强 <<EMAIL>> 1753952365 +0800	reset: moving to 59fcee7c761b3df13eac291e022b027d20457a50
59fcee7c761b3df13eac291e022b027d20457a50 9ba1b13b6a8facb77b928edfb1e623a5a8e05ebb 郝华强 <<EMAIL>> 1753953110 +0800	reset: moving to 9ba1b13b6a8facb77b928edfb1e623a5a8e05ebb
9ba1b13b6a8facb77b928edfb1e623a5a8e05ebb 59fcee7c761b3df13eac291e022b027d20457a50 郝华强 <<EMAIL>> 1753953434 +0800	reset: moving to 59fcee7c761b3df13eac291e022b027d20457a50
59fcee7c761b3df13eac291e022b027d20457a50 3fd69d33467c9a9bacdf5e9973828ab13809ae97 郝华强 <<EMAIL>> 1753956159 +0800	commit: 修复排班会重复的问题
3fd69d33467c9a9bacdf5e9973828ab13809ae97 12776add4ad8d305a910855c81b9cb36545049ae 郝华强 <<EMAIL>> 1754960913 +0800	commit: 修复排班会重复的问题
12776add4ad8d305a910855c81b9cb36545049ae 422a593e31b5b0651d621c6b47573fb46403c63a 郝华强 <<EMAIL>> 1754960956 +0800	pull --no-stat -v --progress origin master: Merge made by the 'ort' strategy.
422a593e31b5b0651d621c6b47573fb46403c63a 094d7e1712651867c5a5e00eb4f6df8d4b91d6e6 郝华强 <<EMAIL>> 1755138077 +0800	commit: 加入违法次数过多自动下派
094d7e1712651867c5a5e00eb4f6df8d4b91d6e6 3726dc1bdaa4e39497abfa93e6f234e59dd01e7a 郝华强 <<EMAIL>> 1755242916 +0800	commit: 修改基础数据
3726dc1bdaa4e39497abfa93e6f234e59dd01e7a 3a7d84e55bfa5e4bc5b4bc82076a7b5851619f73 郝华强 <<EMAIL>> 1755486214 +0800	commit: 加入二次识别日志，修改自动排班bug
3a7d84e55bfa5e4bc5b4bc82076a7b5851619f73 a5bbd054430b6b7af698c2d7c446015036b6c10b 郝华强 <<EMAIL>> 1755508627 +0800	commit: 调整违法二次识别报错问题
a5bbd054430b6b7af698c2d7c446015036b6c10b 163bddb77fc3945e0b63ac45976e6cc0d2876f23 郝华强 <<EMAIL>> 1755570424 +0800	commit: 调整违法二次识别图片丢失问题
163bddb77fc3945e0b63ac45976e6cc0d2876f23 f1f3787c55e5c582cfcd15232878d9bb3148d113 郝华强 <<EMAIL>> 1755744348 +0800	commit: 修改脚本
f1f3787c55e5c582cfcd15232878d9bb3148d113 6446277fd694c39b13b6959d179f25d4ba26ba8f 郝华强 <<EMAIL>> 1755744984 +0800	commit: 修改脚本，加入日志清理
6446277fd694c39b13b6959d179f25d4ba26ba8f d407e3b064f3db8e2b6fa4d48fbabefed65bf5fe 郝华强 <<EMAIL>> 1755765463 +0800	commit: 修改脚本
d407e3b064f3db8e2b6fa4d48fbabefed65bf5fe 17e70f6141f89c8af832bbd63ae9f30e7b860f1f 郝华强 <<EMAIL>> 1755827905 +0800	commit: 修改脚本
17e70f6141f89c8af832bbd63ae9f30e7b860f1f ad331e177517a18f66f790b3c979c520c2de838b 郝华强 <<EMAIL>> 1755831163 +0800	commit: 提取方法，修改报警下派逻辑
ad331e177517a18f66f790b3c979c520c2de838b 553cc111c0e12324991acdc870f274932b609075 郝华强 <<EMAIL>> 1755853825 +0800	commit: 修改脚本
553cc111c0e12324991acdc870f274932b609075 1213c943b8d174a62d27efcba00cc54ee9d05338 郝华强 <<EMAIL>> 1756093052 +0800	pull --no-stat -v --progress origin master: Fast-forward
