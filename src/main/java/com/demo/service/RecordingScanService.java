package com.demo.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.demo.entity.VideoRecording;
import com.demo.mapper.VideoRecordingMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Service;

import java.io.File;
import java.time.LocalDateTime;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 录制文件扫描服务
 * 启动时扫描录制目录，将未入库的文件添加到数据库
 */
@Slf4j
@Service
public class RecordingScanService implements ApplicationRunner {

    private static final String RECORDINGS_BASE_PATH = "/home/<USER>/live/";
    @Autowired
    private VideoRecordingMapper videoRecordingMapper;

    @Override
    public void run(ApplicationArguments args)  {
        log.info("应用启动时开始扫描录制文件目录...");
        scanRecordingDirectory();
    }

    /**
     * 扫描录制目录并同步到数据库
     * 只扫描近三天的目录
     */
    public void scanRecordingDirectory() {
        try {
            File baseDir = new File(RECORDINGS_BASE_PATH);
            if (!baseDir.exists()) {
                log.warn("录制目录不存在: {}", RECORDINGS_BASE_PATH);
//                return SaResult.error("录制目录不存在");
            }
            
            // 获取近三天的日期
            LocalDate today = LocalDate.now();
            Set<String> recentDates = new HashSet<>();
            for (int i = 0; i < 3; i++) {
                LocalDate date = today.minusDays(i);
                // 格式: yyyy-MM-dd
                recentDates.add(date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            }
            
            log.info("将扫描以下日期的目录: {}", recentDates);
            
            int scannedCount = 0;
            int addedCount = 0;
            // 遍历所有流目录
            File[] streamDirs = baseDir.listFiles(File::isDirectory);
            if (streamDirs != null) {
                for (File streamDir : streamDirs) {
                    String streamName = streamDir.getName();
                    log.info("扫描流目录: {}", streamName);
                    int[] counts = scanStreamDirectoryWithDateFilter(streamDir, streamName, recentDates);
                    scannedCount += counts[0];
                    addedCount += counts[1];
                }
            }
            log.info("录制文件扫描完成: 扫描{}个文件，新增{}个记录", scannedCount, addedCount);
//            return SaResult.ok("扫描完成").setData("scanned=" + scannedCount + ", added=" + addedCount);
        } catch (Exception e) {
            log.error("扫描录制文件异常: {}", e.getMessage(), e);
//            return SaResult.error("扫描失败: " + e.getMessage());
        }
    }

    /**
     * 扫描单个流的录制目录
     */
    private int[] scanStreamDirectory(File streamDir, String streamName) {
        int scannedCount = 0;
        int addedCount = 0;

        try {
            // 递归扫描所有.mp4文件
            int[] counts = scanDirectoryRecursive(streamDir, streamName);
            scannedCount = counts[0];
            addedCount = counts[1];
        } catch (Exception e) {
            log.error("扫描流目录异常: {}, error: {}", streamName, e.getMessage());
        }

        return new int[]{scannedCount, addedCount};
    }

    /**
     * 递归扫描目录
     */
    private int[] scanDirectoryRecursive(File dir, String streamName) {
        int scannedCount = 0;
        int addedCount = 0;

        File[] files = dir.listFiles();
        if (files != null) {
            for (File file : files) {
                if (file.isDirectory()) {
                    int[] subCounts = scanDirectoryRecursive(file, streamName);
                    scannedCount += subCounts[0];
                    addedCount += subCounts[1];
                } else if (file.getName().endsWith(".mp4")) {
                    scannedCount++;
                    if (addRecordingToDatabase(file, streamName)) {
                        addedCount++;
                    }
                }
            }
        }

        return new int[]{scannedCount, addedCount};
    }

    /**
     * 将录制文件添加到数据库
     */
    private boolean addRecordingToDatabase(File file, String streamName) {
        try {
            String absolutePath = file.getAbsolutePath();

            // 检查是否已存在
            LambdaQueryWrapper<VideoRecording> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(VideoRecording::getFilePath, absolutePath);
            VideoRecording existing = videoRecordingMapper.selectOne(queryWrapper);

            if (existing != null) {
                // 已存在，跳过
                return false;
            }
            // 解析录制开始时间
            LocalDateTime startTime = parseRecordingStartTime(absolutePath, file);
            // 使用ffprobe获取视频实际时长，计算结束时间
            LocalDateTime endTime = calculateVideoEndTime(absolutePath, startTime);
            // 创建录制记录
            VideoRecording recording = new VideoRecording();
            recording.setStreamName(streamName);
            recording.setFilePath(absolutePath);
            recording.setFileName(file.getName());
            recording.setFileSize(file.length());
            recording.setStartTime(startTime);
            recording.setEndTime(endTime);
            recording.setCreatedTime(LocalDateTime.now());
            recording.setStatus(VideoRecording.STATUS_COMPLETED);
            // 计算录制时长
            long durationSeconds = java.time.Duration.between(startTime, endTime).getSeconds();
            recording.setDuration((int) durationSeconds);

            // 保存到数据库
            videoRecordingMapper.insert(recording);

            log.debug("添加录制文件到数据库: {}", file.getName());
            return true;
        } catch (Exception e) {
            log.error("添加录制文件到数据库失败: {}, error: {}", file.getName(), e.getMessage());
            return false;
        }
    }

    /**
     * 解析录制开始时间
     */
    private LocalDateTime parseRecordingStartTime(String filePath, File file) {
        try {
            // 尝试从文件名解析
            String fileName = file.getName();
            if (fileName.matches("[0-9]{2}\\.[0-9]{2}\\.[0-9]{2}\\.[0-9]{3}\\.mp4")) {
                String[] parts = fileName.replace(".mp4", "").split("\\.");
                String[] pathParts = filePath.split("/");

                if (parts.length == 4 && pathParts.length >= 4) {
                    int year = Integer.parseInt(pathParts[pathParts.length - 4]);
                    int month = Integer.parseInt(pathParts[pathParts.length - 3]);
                    int day = Integer.parseInt(pathParts[pathParts.length - 2]);
                    int hour = Integer.parseInt(parts[0]);
                    int minute = Integer.parseInt(parts[1]);
                    int second = Integer.parseInt(parts[2]);
                    int millisecond = Integer.parseInt(parts[3]);

                    return LocalDateTime.of(year, month, day, hour, minute, second, millisecond * 1000000);
                }
            }
            // 备选方案：使用文件修改时间减去30分钟
            LocalDateTime fileTime = LocalDateTime.ofInstant(
                    java.time.Instant.ofEpochMilli(file.lastModified()),
                    java.time.ZoneId.systemDefault()
            );
            return fileTime.minusMinutes(30);

        } catch (Exception e) {
            log.warn("解析录制开始时间失败: {}, error: {}", filePath, e.getMessage());
            // 使用文件修改时间减去30分钟作为默认值
            LocalDateTime fileTime = LocalDateTime.ofInstant(
                    java.time.Instant.ofEpochMilli(file.lastModified()),
                    java.time.ZoneId.systemDefault()
            );
            return fileTime.minusMinutes(30);
        }
    }

    /**
     * 计算视频结束时间
     * 使用ffprobe获取视频实际时长，基于开始时间计算结束时间
     */
    private LocalDateTime calculateVideoEndTime(String videoFilePath, LocalDateTime startTime) {
        try {
            // 使用ffprobe获取视频时长
            List<String> command = Arrays.asList(
                "ffprobe",
                "-v", "quiet",
                "-show_entries", "format=duration",
                "-of", "csv=p=0",
                videoFilePath
            );

            ProcessBuilder pb = new ProcessBuilder(command);
            Process process = pb.start();

            StringBuilder output = new StringBuilder();
            try (java.io.BufferedReader reader = new java.io.BufferedReader(new java.io.InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line);
                }
            }

            boolean finished = process.waitFor(10, java.util.concurrent.TimeUnit.SECONDS);
            if (!finished) {
                process.destroyForcibly();
                log.warn("ffprobe获取视频时长超时，使用文件修改时间: {}", videoFilePath);
                return getFileModificationTime(videoFilePath);
            }

            if (process.exitValue() == 0) {
                String durationStr = output.toString().trim();
                if (!durationStr.isEmpty()) {
                    double duration = Double.parseDouble(durationStr);
                    LocalDateTime endTime = startTime.plusSeconds((long) duration);
                    log.debug("通过ffprobe计算视频结束时间: 开始={}, 时长={}秒, 结束={}",
                            startTime, duration, endTime);
                    return endTime;
                }
            }

            log.warn("ffprobe获取视频时长失败，使用文件修改时间: {}", videoFilePath);
            return getFileModificationTime(videoFilePath);

        } catch (Exception e) {
            log.error("计算视频结束时间时发生异常，使用文件修改时间: {}", videoFilePath, e);
            return getFileModificationTime(videoFilePath);
        }
    }

    /**
     * 获取文件修改时间（备用方案）
     */
    private LocalDateTime getFileModificationTime(String videoFilePath) {
        try {
            java.io.File file = new java.io.File(videoFilePath);
            return LocalDateTime.ofInstant(
                java.time.Instant.ofEpochMilli(file.lastModified()),
                java.time.ZoneId.systemDefault()
            );
        } catch (Exception e) {
            log.error("获取文件修改时间失败: {}", videoFilePath, e);
            return LocalDateTime.now();
        }
    }
    
    /**
     * 扫描单个流目录，只处理指定日期的文件
     */
    private int[] scanStreamDirectoryWithDateFilter(File streamDir, String streamName, Set<String> recentDates) {
        int scannedCount = 0;
        int addedCount = 0;

        try {
            int[] counts = scanDirectoryRecursiveWithDateFilter(streamDir, streamName, recentDates);
            scannedCount = counts[0];
            addedCount = counts[1];
        } catch (Exception e) {
            log.error("扫描流目录异常: {}, error: {}", streamName, e.getMessage());
        }

        return new int[]{scannedCount, addedCount};
    }

    /**
     * 递归扫描目录，只处理指定日期的.mp4文件
     */
    private int[] scanDirectoryRecursiveWithDateFilter(File dir, String streamName, Set<String> recentDates) {
        int scannedCount = 0;
        int addedCount = 0;

        File[] files = dir.listFiles();
        if (files != null) {
            for (File file : files) {
                if (file.isDirectory()) {
                    // 检查目录名是否匹配近三天的日期
                    if (shouldScanDirectory(file, recentDates)) {
                        int[] subCounts = scanDirectoryRecursiveWithDateFilter(file, streamName, recentDates);
                        scannedCount += subCounts[0];
                        addedCount += subCounts[1];
                    }
                } else if (file.getName().endsWith(".mp4")) {
                    // 检查文件路径是否包含近三天的日期
                    if (isRecentFile(file, recentDates)) {
                        scannedCount++;
                        if (addRecordingToDatabase(file, streamName)) {
                            addedCount++;
                        }
                    }
                }
            }
        }

        return new int[]{scannedCount, addedCount};
    }

    /**
     * 判断是否应该扫描该目录（基于日期）
     */
    private boolean shouldScanDirectory(File dir, Set<String> recentDates) {
        // 检查目录路径是否包含近三天的日期
        String path = dir.getAbsolutePath();
        for (String date : recentDates) {
            if (path.contains(date.replace("-", "/"))) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断文件是否为近三天内的文件
     */
    private boolean isRecentFile(File file, Set<String> recentDates) {
        // 检查文件路径是否包含近三天的日期
        String path = file.getAbsolutePath();
        for (String date : recentDates) {
            if (path.contains(date.replace("-", "/"))) {
                return true;
            }
        }
        return false;
    }
}
