package com.demo.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.ResourceAccessException;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * SRS API服务
 * 用于与SRS服务器进行API交互，主动控制推流
 */
@Slf4j
@Service
public class SrsApiService {

    @Value("${srs.api.url:http://localhost:1985}")
    private String srsApiUrl;

    private final RestTemplate restTemplate;

    public SrsApiService() {
        this.restTemplate = new RestTemplate();
    }

    /**
     * 获取所有活跃的推流
     * @return 推流列表
     */
    public List<Map<String, Object>> getActiveStreams() {
        try {
            String url = srsApiUrl + "/api/v1/streams/";
            ResponseEntity<Map> response = restTemplate.getForEntity(url, Map.class);
            
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Map<String, Object> body = response.getBody();
                if (body.get("streams") instanceof List) {
                    return (List<Map<String, Object>>) body.get("streams");
                }
            }
        } catch (Exception e) {
            log.warn("获取SRS活跃推流失败: {}", e.getMessage());
        }
        return List.of();
    }

    /**
     * 主动断开指定的推流
     * @param streamName 流名称
     * @return 是否成功断开
     */
    public boolean kickoffStream(String streamName) {
        try {
            // 首先获取流的详细信息
            String streamUrl = srsApiUrl + "/api/v1/streams/" + streamName;
            ResponseEntity<Map> streamResponse = restTemplate.getForEntity(streamUrl, Map.class);
            
            if (streamResponse.getStatusCode() != HttpStatus.OK || streamResponse.getBody() == null) {
                log.warn("无法获取流 {} 的信息", streamName);
                return false;
            }

            Map<String, Object> streamInfo = streamResponse.getBody();
            Map<String, Object> stream = (Map<String, Object>) streamInfo.get("stream");
            
            if (stream == null) {
                log.warn("流 {} 不存在或未推流", streamName);
                return false;
            }

            // 获取客户端ID
            String clientId = (String) stream.get("id");
            if (clientId == null) {
                log.warn("无法获取流 {} 的客户端ID", streamName);
                return false;
            }

            // 断开推流客户端
            String kickoffUrl = srsApiUrl + "/api/v1/clients/" + clientId;
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            HttpEntity<String> request = new HttpEntity<>("{}", headers);
            ResponseEntity<Map> kickoffResponse = restTemplate.exchange(
                kickoffUrl, HttpMethod.DELETE, request, Map.class);

            if (kickoffResponse.getStatusCode() == HttpStatus.OK) {
                log.info("成功断开推流 - 流: {}, 客户端ID: {}", streamName, clientId);
                return true;
            } else {
                log.warn("断开推流失败 - 流: {}, 响应状态: {}", streamName, kickoffResponse.getStatusCode());
                return false;
            }

        } catch (HttpClientErrorException e) {
            if (e.getStatusCode() == HttpStatus.NOT_FOUND) {
                log.info("流 {} 已不存在，可能已自然断开", streamName);
                return true; // 流已不存在，认为断开成功
            } else {
                log.error("断开推流时发生HTTP错误 - 流: {}, 错误: {}", streamName, e.getMessage());
                return false;
            }
        } catch (ResourceAccessException e) {
            log.error("无法连接到SRS API服务器 - URL: {}, 错误: {}", srsApiUrl, e.getMessage());
            return false;
        } catch (Exception e) {
            log.error("断开推流时发生异常 - 流: {}, 错误: {}", streamName, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 批量断开多个推流
     * @param streamNames 流名称列表
     * @return 成功断开的数量
     */
    public int kickoffStreams(List<String> streamNames) {
        int successCount = 0;
        for (String streamName : streamNames) {
            if (kickoffStream(streamName)) {
                successCount++;
            }
            // 添加短暂延迟，避免对SRS服务器造成压力
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
        return successCount;
    }

    /**
     * 检查SRS API服务器是否可用
     * @return 是否可用
     */
    public boolean isApiAvailable() {
        try {
            String url = srsApiUrl + "/api/v1/summaries";
            ResponseEntity<Map> response = restTemplate.getForEntity(url, Map.class);
            return response.getStatusCode() == HttpStatus.OK;
        } catch (Exception e) {
            log.debug("SRS API服务器不可用: {}", e.getMessage());
            return false;
        }
    }
}
