package com.demo.service.impl;

import cn.dev33.satoken.util.SaResult;
import com.demo.service.VideoProcessingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.File;
import java.io.InputStreamReader;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 视频处理服务实现类
 */
@Slf4j
@Service
public class VideoProcessingServiceImpl implements VideoProcessingService {

    // 视频录制根目录
    private static final String VIDEO_ROOT_PATH = "/home/<USER>/live";
    
    // 违法视频输出目录
    private static final String ILLEGAL_VIDEO_OUTPUT_PATH = "/home/<USER>/IllegalVideos";
    
    // 时间格式化器
    private static final DateTimeFormatter PATH_FORMATTER = DateTimeFormatter.ofPattern("yyyy/MM/dd");

    @Override
    public SaResult processIllegalVideo(String illegalRecordId, LocalDateTime createTime, String equipmentNumber) {
        try {
            log.info("开始处理违法记录视频截取，记录ID: {}, 创建时间: {}, 设备编号: {}", 
                    illegalRecordId, createTime, equipmentNumber);

            // 计算截取时间范围（前后10秒）
            LocalDateTime startTime = createTime.minusSeconds(10);
            LocalDateTime endTime = createTime.plusSeconds(10);

            // 查找对应的视频文件
            List<String> videoFiles = findVideoFiles(equipmentNumber, startTime, endTime);
            
            if (videoFiles.isEmpty()) {
                log.warn("未找到对应时间段的视频文件，设备编号: {}, 时间范围: {} - {}", 
                        equipmentNumber, startTime, endTime);
                return SaResult.error("未找到对应的视频文件");
            }

            // 创建输出目录
            File outputDir = new File(ILLEGAL_VIDEO_OUTPUT_PATH);
            if (!outputDir.exists()) {
                outputDir.mkdirs();
            }

            // 生成输出文件名
            String outputFileName = String.format("%s_%s.mp4", 
                    illegalRecordId, 
                    createTime.format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")));
            String outputPath = ILLEGAL_VIDEO_OUTPUT_PATH + "/" + outputFileName;

            if (videoFiles.size() == 1) {
                // 单个视频文件，直接截取
                return extractVideoSegment(videoFiles.get(0), startTime, endTime, outputPath);
            } else {
                // 多个视频文件，需要截取后合成
                List<String> segments = new ArrayList<>();
                
                for (int i = 0; i < videoFiles.size(); i++) {
                    String videoFile = videoFiles.get(i);
                    LocalDateTime[] timeRange = getVideoTimeRange(videoFile);
                    
                    // 计算当前文件的截取时间范围
                    LocalDateTime segmentStart = i == 0 ? startTime : timeRange[0];
                    LocalDateTime segmentEnd = i == videoFiles.size() - 1 ? endTime : timeRange[1];
                    
                    // 生成临时片段文件名
                    String segmentPath = ILLEGAL_VIDEO_OUTPUT_PATH + "/temp_" + illegalRecordId + "_" + i + ".mp4";
                    
                    SaResult segmentResult = extractVideoSegment(videoFile, segmentStart, segmentEnd, segmentPath);
                    if (segmentResult.getCode() == 200) {
                        segments.add(segmentPath);
                    } else {
                        log.error("视频片段截取失败: {}", segmentResult.getMsg());
                        // 清理已生成的临时文件
                        cleanupTempFiles(segments);
                        return segmentResult;
                    }
                }
                
                // 合成视频片段
                SaResult mergeResult = mergeVideoSegments(segments, outputPath);
                
                // 清理临时文件
                cleanupTempFiles(segments);
                
                return mergeResult;
            }

        } catch (Exception e) {
            log.error("处理违法视频时发生异常", e);
            return SaResult.error("视频处理失败: " + e.getMessage());
        }
    }

    @Override
    public SaResult extractVideoSegment(String videoFilePath, LocalDateTime startTime, LocalDateTime endTime, String outputPath) {
        try {
            log.info("开始截取视频片段，输入: {}, 输出: {}, 时间范围: {} - {}",
                    videoFilePath, outputPath, startTime, endTime);

            // 检查输入文件是否存在
            if (!isVideoFileExists(videoFilePath)) {
                return SaResult.error("输入视频文件不存在: " + videoFilePath);
            }

            // 获取视频文件的时间范围和实际时长
            LocalDateTime[] videoTimeRange = getVideoTimeRange(videoFilePath);
            long videoActualDuration = getVideoDurationSeconds(videoFilePath);

            log.info("=== 时间计算详细日志 ===");
            log.info("视频文件路径: {}", videoFilePath);
            log.info("视频文件开始时间: {}", videoTimeRange[0]);
            log.info("视频文件结束时间: {}", videoTimeRange[1]);
            log.info("视频实际时长: {}秒", videoActualDuration);
            log.info("请求截取开始时间: {}", startTime);
            log.info("请求截取结束时间: {}", endTime);

            // 计算相对于视频开始时间的偏移量（秒）
            long startOffset = java.time.Duration.between(videoTimeRange[0], startTime).getSeconds();
            long requestedDuration = java.time.Duration.between(startTime, endTime).getSeconds();

            log.info("计算的开始偏移量: {}秒", startOffset);
            log.info("请求的截取时长: {}秒", requestedDuration);

            // 检查时间范围是否有效
            if (startOffset < 0) {
                log.warn("开始时间早于视频开始时间，调整偏移量: {} -> 0", startOffset);
                requestedDuration += startOffset; // 减少时长
                startOffset = 0;
            }

            // 检查是否超出视频实际时长
            if (startOffset >= videoActualDuration) {
                return SaResult.error("截取开始时间超出视频范围，偏移量: " + startOffset + "秒，视频时长: " + videoActualDuration + "秒");
            }

            // 调整截取时长，确保不超出视频范围
            long maxAvailableDuration = videoActualDuration - startOffset;
            long actualDuration = Math.min(requestedDuration, maxAvailableDuration);

            if (actualDuration <= 0) {
                return SaResult.error("截取时长无效，计算时长: " + actualDuration + "秒");
            }

            log.info("最终截取参数: 偏移量={}秒, 时长={}秒", startOffset, actualDuration);
            log.info("预期截取的实际时间范围: {} - {}",
                    videoTimeRange[0].plusSeconds(startOffset),
                    videoTimeRange[0].plusSeconds(startOffset + actualDuration));
            log.info("=== 时间计算日志结束 ===");

            // 构建FFmpeg命令 - 简化参数避免兼容性问题
            List<String> command = Arrays.asList(
                "ffmpeg",
                "-i", videoFilePath,
                "-ss", String.valueOf(startOffset),
                "-t", String.valueOf(actualDuration),
                "-c", "copy", // 直接复制，不重编码
                "-avoid_negative_ts", "make_zero",
                "-y", // 覆盖输出文件
                outputPath
            );

            log.info("执行FFmpeg命令: {}", String.join(" ", command));

            // 执行FFmpeg命令
            ProcessBuilder pb = new ProcessBuilder(command);
            pb.redirectErrorStream(true);
            Process process = pb.start();

            // 读取命令输出
            StringBuilder output = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                }
            }

            // 等待命令执行完成
            boolean finished = process.waitFor(30, TimeUnit.SECONDS);
            if (!finished) {
                process.destroyForcibly();
                return SaResult.error("视频截取超时");
            }

            if (process.exitValue() == 0) {
                log.info("视频截取成功: {}", outputPath);

                // 验证输出文件
                File outputFile = new File(outputPath);
                if (outputFile.exists() && outputFile.length() > 0) {
                    log.info("输出文件验证成功，大小: {} 字节", outputFile.length());

                    // 尝试获取输出文件的实际时间信息进行验证
                    try {
                        LocalDateTime[] outputTimeRange = getVideoTimeRange(outputPath);
                        log.info("输出视频的时间范围: {} - {}", outputTimeRange[0], outputTimeRange[1]);
                    } catch (Exception e) {
                        log.warn("无法解析输出视频时间范围: {}", e.getMessage());
                    }

                    return SaResult.ok("视频截取成功").setData(outputPath);
                } else {
                    log.error("输出文件不存在或为空: {}", outputPath);
                    return SaResult.error("视频截取失败：输出文件无效");
                }
            } else {
                log.error("FFmpeg执行失败，退出码: {}, 输出: {}", process.exitValue(), output.toString());
                return SaResult.error("视频截取失败：FFmpeg执行错误");
            }

        } catch (Exception e) {
            log.error("视频截取时发生异常", e);
            return SaResult.error("视频截取异常: " + e.getMessage());
        }
    }

    @Override
    public SaResult mergeVideoSegments(List<String> videoSegments, String outputPath) {
        try {
            log.info("开始合成视频片段，片段数量: {}, 输出: {}", videoSegments.size(), outputPath);

            if (videoSegments.isEmpty()) {
                return SaResult.error("没有视频片段需要合成");
            }

            if (videoSegments.size() == 1) {
                // 只有一个片段，直接重命名
                File sourceFile = new File(videoSegments.get(0));
                File targetFile = new File(outputPath);
                if (sourceFile.renameTo(targetFile)) {
                    return SaResult.ok("视频合成成功").setData(outputPath);
                } else {
                    return SaResult.error("文件重命名失败");
                }
            }

            // 创建文件列表文件
            String listFilePath = ILLEGAL_VIDEO_OUTPUT_PATH + "/temp_list.txt";
            try (java.io.PrintWriter writer = new java.io.PrintWriter(listFilePath)) {
                for (String segment : videoSegments) {
                    writer.println("file '" + segment + "'");
                }
            }

            // 构建FFmpeg合成命令 - 简化参数
            List<String> command = Arrays.asList(
                "ffmpeg",
                "-f", "concat",
                "-safe", "0",
                "-i", listFilePath,
                "-c", "copy", // 直接复制，不重编码
                "-y", // 覆盖输出文件
                outputPath
            );

            // 执行FFmpeg命令
            ProcessBuilder pb = new ProcessBuilder(command);
            pb.redirectErrorStream(true);
            Process process = pb.start();

            // 读取命令输出
            StringBuilder output = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                }
            }

            // 等待命令执行完成
            boolean finished = process.waitFor(60, TimeUnit.SECONDS);
            if (!finished) {
                process.destroyForcibly();
                return SaResult.error("视频合成超时");
            }

            // 清理临时文件列表
            new File(listFilePath).delete();

            if (process.exitValue() == 0) {
                log.info("视频合成成功: {}", outputPath);
                return SaResult.ok("视频合成成功").setData(outputPath);
            } else {
                log.error("FFmpeg合成失败，退出码: {}, 输出: {}", process.exitValue(), output.toString());
                return SaResult.error("视频合成失败");
            }

        } catch (Exception e) {
            log.error("视频合成时发生异常", e);
            return SaResult.error("视频合成异常: " + e.getMessage());
        }
    }

    @Override
    public List<String> findVideoFiles(String equipmentNumber, LocalDateTime startTime, LocalDateTime endTime) {
        List<String> videoFiles = new ArrayList<>();

        try {
            // 构建两个摄像头的流名称
            String streamName1 = buildStreamName(equipmentNumber, false); // 1921680120
            String streamName2 = buildStreamName(equipmentNumber, true);  // 1921680130

            // 查找两个摄像头的视频文件
            videoFiles.addAll(findVideoFilesForStream(streamName1, startTime, endTime));
            videoFiles.addAll(findVideoFilesForStream(streamName2, startTime, endTime));

            // 按时间排序
            videoFiles.sort((a, b) -> {
                LocalDateTime[] timeA = getVideoTimeRange(a);
                LocalDateTime[] timeB = getVideoTimeRange(b);
                return timeA[0].compareTo(timeB[0]);
            });

            log.info("找到 {} 个相关视频文件，设备编号: {}, 时间范围: {} - {}",
                    videoFiles.size(), equipmentNumber, startTime, endTime);

        } catch (Exception e) {
            log.error("查找视频文件时发生异常", e);
        }

        return videoFiles;
    }

    @Override
    public String buildStreamName(String equipmentNumber, boolean isSecondCamera) {
        // 去掉冒号：c0:74:2b:fc:29:f1 -> c0742bfc29f1
        String cleanEquipmentNumber = equipmentNumber.replace(":", "");

        // 拼接摄像头标识
        String suffix = isSecondCamera ? "1921680130" : "1921680120";

        return cleanEquipmentNumber + suffix;
    }

    @Override
    public boolean isVideoFileExists(String filePath) {
        return new File(filePath).exists();
    }

    @Override
    public LocalDateTime[] getVideoTimeRange(String filePath) {
        try {
            // 从文件路径解析开始时间
            // 路径格式: /home/<USER>/live/{streamName}/2025/07/22/09.47.14.968.mp4
            String pathPattern = ".*/([0-9]{4})/([0-9]{2})/([0-9]{2})/([0-9]{2})\\.([0-9]{2})\\.([0-9]{2})\\.([0-9]{3})\\.mp4$";
            java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(pathPattern);
            java.util.regex.Matcher matcher = pattern.matcher(filePath);

            if (matcher.find()) {
                int year = Integer.parseInt(matcher.group(1));
                int month = Integer.parseInt(matcher.group(2));
                int day = Integer.parseInt(matcher.group(3));
                int hour = Integer.parseInt(matcher.group(4));
                int minute = Integer.parseInt(matcher.group(5));
                int second = Integer.parseInt(matcher.group(6));
                int millis = Integer.parseInt(matcher.group(7));

                LocalDateTime startTime = LocalDateTime.of(year, month, day, hour, minute, second, millis * 1000000);

                // 使用ffprobe获取视频实际时长，计算结束时间
                long durationSeconds = getVideoDurationSeconds(filePath);
                LocalDateTime endTime = startTime.plusSeconds(durationSeconds);

                log.debug("视频时间范围解析: 文件={}, 开始={}, 时长={}秒, 结束={}",
                        filePath, startTime, durationSeconds, endTime);

                return new LocalDateTime[]{startTime, endTime};
            }
        } catch (Exception e) {
            log.error("解析视频文件时间范围失败: {}", filePath, e);
        }

        // 如果解析失败，返回默认值
        LocalDateTime now = LocalDateTime.now();
        return new LocalDateTime[]{now.minusMinutes(30), now};
    }

    /**
     * 查找指定流的视频文件
     */
    private List<String> findVideoFilesForStream(String streamName, LocalDateTime startTime, LocalDateTime endTime) {
        List<String> files = new ArrayList<>();

        try {
            // 构建基础路径
            String basePath = VIDEO_ROOT_PATH + "/" + streamName;

            // 遍历可能的日期目录
            LocalDateTime current = startTime.toLocalDate().atStartOfDay();
            LocalDateTime searchEnd = endTime.toLocalDate().plusDays(1).atStartOfDay();

            while (current.isBefore(searchEnd)) {
                String datePath = basePath + "/" + current.format(PATH_FORMATTER);
                File dateDir = new File(datePath);

                if (dateDir.exists() && dateDir.isDirectory()) {
                    File[] videoFiles = dateDir.listFiles((dir, name) -> name.endsWith(".mp4"));
                    if (videoFiles != null) {
                        for (File videoFile : videoFiles) {
                            LocalDateTime[] timeRange = getVideoTimeRange(videoFile.getAbsolutePath());

                            // 检查视频时间范围是否与目标时间范围有重叠
                            if (timeRange[1].isAfter(startTime) && timeRange[0].isBefore(endTime)) {
                                files.add(videoFile.getAbsolutePath());
                            }
                        }
                    }
                }

                current = current.plusDays(1);
            }

        } catch (Exception e) {
            log.error("查找流视频文件时发生异常，流名称: {}", streamName, e);
        }

        return files;
    }

    /**
     * 获取视频文件的实际时长（秒）
     */
    private long getVideoDurationSeconds(String videoFilePath) {
        try {
            // 使用ffprobe获取视频时长
            List<String> command = Arrays.asList(
                "ffprobe",
                "-v", "quiet",
                "-show_entries", "format=duration",
                "-of", "csv=p=0",
                videoFilePath
            );

            ProcessBuilder pb = new ProcessBuilder(command);
            Process process = pb.start();

            StringBuilder output = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line);
                }
            }

            boolean finished = process.waitFor(10, TimeUnit.SECONDS);
            if (!finished) {
                process.destroyForcibly();
                log.warn("ffprobe获取视频时长超时: {}", videoFilePath);
                return estimateDurationFromFileSize(videoFilePath);
            }

            if (process.exitValue() == 0) {
                String durationStr = output.toString().trim();
                if (!durationStr.isEmpty()) {
                    double duration = Double.parseDouble(durationStr);
                    return (long) duration;
                }
            }

            log.warn("ffprobe获取视频时长失败: {}", videoFilePath);
            return estimateDurationFromFileSize(videoFilePath);

        } catch (Exception e) {
            log.error("获取视频时长时发生异常: {}", videoFilePath, e);
            return estimateDurationFromFileSize(videoFilePath);
        }
    }

    /**
     * 根据文件大小估算视频时长（备用方案）
     */
    private long estimateDurationFromFileSize(String videoFilePath) {
        try {
            File file = new File(videoFilePath);
            long fileSize = file.length();
            // 假设平均码率为2Mbps，估算时长
            long estimatedDuration = fileSize / (2 * 1024 * 1024 / 8); // 字节转秒
            log.info("根据文件大小估算视频时长: {}秒, 文件: {}", estimatedDuration, videoFilePath);
            return Math.max(estimatedDuration, 60); // 最少60秒
        } catch (Exception e) {
            log.error("估算视频时长失败: {}", videoFilePath, e);
            return 300; // 默认5分钟
        }
    }

    /**
     * 清理临时文件
     */
    private void cleanupTempFiles(List<String> tempFiles) {
        for (String tempFile : tempFiles) {
            try {
                new File(tempFile).delete();
                log.debug("清理临时文件: {}", tempFile);
            } catch (Exception e) {
                log.warn("清理临时文件失败: {}", tempFile, e);
            }
        }
    }
}
