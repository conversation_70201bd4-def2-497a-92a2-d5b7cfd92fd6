package com.demo.config;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.demo.entity.*;
import com.demo.enums.AttendanceEnum;
import com.demo.enums.HandleEnum;
import com.demo.enums.LeaveStatusEnum;
import com.demo.mapper.AccuratePersuasionMapper;
import com.demo.mapper.AttendanceMapper;
import com.demo.mapper.IllegalRecordsMapper;
import com.demo.mapper.LeaveMapper;
import com.demo.mapper.ScheduleMapper;
import com.demo.service.*;
import com.demo.service.impl.LeavePostServiceImpl;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

import static com.demo.config.RelatedConfigurations.relatedconfigurations;

@Slf4j
@Component
public class scheduledTasks {
    @Autowired
    SysOperLogService sysOperLogService;
    @Autowired
    UserShiftGroupService userShiftGroupService;  // 用户班次组关系服务
    @Autowired
    ScheduleMapper scheduleMapper;
    @Autowired
    ScheduleService scheduleService;
    @Autowired
    AttendanceMapper attendanceMapper;
    @Autowired
    ShiftService shiftService;
    @Autowired
    LeavePostServiceImpl leavePostServiceImpl;
    @Autowired
    LeavePostService leavePostService;
    @Autowired
    LeaveMapper leaveMapper;
    @Autowired
    DeviceService deviceService;
    @Autowired
    AccuratePersuasionMapper accuratePersuasionMapper;
    @Autowired
    IllegalRecordsMapper illegalRecordsMapper;
    @Autowired
    VideoRecordingService videoRecordingService;
    @Autowired
    VideoProcessingService videoProcessingService;
    @Autowired
    StreamService streamService;
    // 脱岗判定阈值（分钟）
    //private static final int LEAVE_POST_THRESHOLD = 15;

    // 每次处理的批次大小
    private static final int BATCH_SIZE = 100;

    ///**
    // * 定时任务，清除日志
    // */scheduledTasks
    @Scheduled(cron = "0 0 0 * * ?") // 每天0点执行
    public void clearingLog() {
        Integer logSaveTime = relatedconfigurations.getLogSaveTime();
        LocalDate today = LocalDate.now();
        LocalDate dateBefore = today.minusDays(logSaveTime);
        QueryWrapper<SysOperLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.le("oper_time", dateBefore);
        sysOperLogService.remove(queryWrapper);
        log.info("定时任务执行完毕,删除日志成功");
    }

    /**
     * 每天2点自动为指定班组的劝导员生成未来一个月的排班
     * 已优化：避免重复排班，使用更精确的检查逻辑
     */
//    @Scheduled(cron = "0 0 0 15 * ?")
    @Scheduled(cron = "0 0 2 * * ?") // 每天2点执行
    @Transactional
    public void autoGenerateMonthlySchedule() {
        log.info("开始执行自动排班任务...");
        List<Integer> userIds = userShiftGroupService.getAllCounselorsWithShiftGroup();
        log.info("找到{}个需要排班的用户", userIds.size());

        LocalDateTime startDate = LocalDateTime.now().withDayOfMonth(1).plusMonths(1);
        LocalDateTime endDate = startDate.withDayOfMonth(startDate.toLocalDate().lengthOfMonth());
        int totalDaysInMonth = startDate.toLocalDate().lengthOfMonth();

        int totalGenerated = 0;
        int totalSkipped = 0;

        for (Integer userId : userIds) {
            try {
                // 使用改进后的排班生成方法，内置重复检查
                List<Schedule> generatedSchedules = scheduleService.generateSchedule(userId, startDate, totalDaysInMonth);

                // 统计实际生成的排班数量
                List<Schedule> existingSchedules = scheduleMapper.getBasicSchedulingInformation(userId, startDate, endDate);
                int actualGenerated = generatedSchedules.size();
                int skipped = totalDaysInMonth - actualGenerated;

                totalGenerated += actualGenerated;
                totalSkipped += skipped;

                log.debug("用户[{}]排班生成完成：新增{}条，跳过重复{}条", userId, actualGenerated, skipped);

            } catch (Exception e) {
                log.error("用户[{}]排班生成失败: {}", userId, e.getMessage(), e);
            }
        }

        log.info("自动排班任务完成：共生成{}条新排班，跳过{}条重复排班", totalGenerated, totalSkipped);
    }

    /**
     * 定时任务：每天处理前一天的考勤异常情况
     * 重新设计的逻辑，更加清晰和简洁
     */
    @Scheduled(cron = "0 0 1 * * ?")
    @Transactional
    public void processUnmarkedAttendance() {
        log.info("开始执行考勤异常处理定时任务...");

        LocalDateTime now = LocalDateTime.now();
        LocalDate yesterday = now.minusDays(1).toLocalDate();

        try {
            List<Schedule> schedules = scheduleMapper.selectSchedulesByDate(yesterday);
            log.info("找到 {} 条前一天的排班记录", schedules.size());

            int processedCount = 0;
            int absentCount = 0;
            int noCheckOutCount = 0;

            for (Schedule schedule : schedules) {
                try {
                    AttendanceProcessResult result = processScheduleAttendance(schedule, now);
                    if (result.isProcessed()) {
                        processedCount++;
                        if (result.getType() == AttendanceProcessType.ABSENT) {
                            absentCount++;
                        } else if (result.getType() == AttendanceProcessType.NO_CHECK_OUT) {
                            noCheckOutCount++;
                        }
                    }
                } catch (Exception e) {
                    log.error("处理排班记录异常，scheduleId: {}, userId: {}",
                             schedule.getId(), schedule.getUserId(), e);
                }
            }

            log.info("考勤异常处理完成 - 总处理: {}, 缺勤: {}, 忘记签退: {}",
                    processedCount, absentCount, noCheckOutCount);

        } catch (Exception e) {
            log.error("考勤异常处理定时任务执行失败", e);
        }
    }

    /**
     * 处理单个排班的考勤异常情况
     * 简化逻辑：只处理两种情况
     * @param schedule 排班记录
     * @param now 当前时间
     * @return 处理结果
     */
    private AttendanceProcessResult processScheduleAttendance(Schedule schedule, LocalDateTime now) {
        // 获取班次信息
        Shift shift = shiftService.getById(schedule.getShiftId());
        if (shift == null) {
            log.warn("未找到班次信息，scheduleId: {}, shiftId: {}",
                    schedule.getId(), schedule.getShiftId());
            return AttendanceProcessResult.notProcessed();
        }

        // 计算班次时间
        LocalDateTime scheduleStart = schedule.getScheduleDate().with(shift.getStartTime());
        LocalDateTime scheduleEnd = schedule.getScheduleDate().with(shift.getEndTime());

        // 只处理已经结束的班次
        if (!now.isAfter(scheduleEnd)) {
            return AttendanceProcessResult.notProcessed();
        }

        // 获取该排班的所有考勤记录
        List<Attendance> attendances = attendanceMapper.selectByScheduleId(schedule.getId());

        // 情况1：完全没有考勤记录 - 标记为缺勤
        if (attendances.isEmpty()) {
            createAbsentRecord(schedule, scheduleStart, now, "完全缺勤");
            return AttendanceProcessResult.processed(AttendanceProcessType.ABSENT);
        }

        // 情况2：有考勤记录，检查是否有未签退的记录
        Attendance incompleteRecord = attendances.stream()
                .filter(a -> a.getCheckInTime() != null && a.getCheckOutTime() == null)
                .findFirst()
                .orElse(null);

        if (incompleteRecord != null) {
            // 有签到但没有签退 - 补充签退时间并标记为早退
            handleMissingCheckOut(incompleteRecord, scheduleEnd, now);
            return AttendanceProcessResult.processed(AttendanceProcessType.NO_CHECK_OUT);
        }

        // 已经有完整的考勤记录，无需处理
        return AttendanceProcessResult.notProcessed();
    }
    /**
     * 创建缺勤记录
     */
    private void createAbsentRecord(Schedule schedule, LocalDateTime scheduleStart, LocalDateTime now, String reason) {
        Attendance absentRecord = new Attendance();
        absentRecord.setUserId(schedule.getUserId());
        absentRecord.setUserName(schedule.getUserName());
        absentRecord.setScheduleId(schedule.getId());
        absentRecord.setStatus(AttendanceEnum.ABSENCE_FROM_DUTY);
        absentRecord.setType(AttendanceEnum.ABSENCE_FROM_DUTY.getCode()); // 统一使用缺勤的code
        absentRecord.setRemark("系统自动标记：" + reason);
        absentRecord.setCreateTime(now);
        absentRecord.setUpdateTime(now);
        // 设置默认的签到时间为班次开始时间，便于统计
        absentRecord.setCheckInTime(scheduleStart);

        attendanceMapper.insert(absentRecord);
        log.info("用户 {} 的班次 {} 标记为缺勤：{}", schedule.getUserId(), schedule.getId(), reason);
    }



    /**
     * 处理忘记签退的情况
     */
    private void handleMissingCheckOut(Attendance checkInRecord, LocalDateTime scheduleEnd, LocalDateTime now) {
        checkInRecord.setStatus(AttendanceEnum.LEAVE_EARLY);
        checkInRecord.setCheckOutTime(scheduleEnd); // 设为班次结束时间
        checkInRecord.setUpdateTime(now);
        checkInRecord.setRemark("系统自动补充签退时间");

        attendanceMapper.updateById(checkInRecord);
        log.info("用户 {} 的考勤记录 {} 补充签退时间", checkInRecord.getUserId(), checkInRecord.getId());
    }

    /**
     * 考勤处理结果类型枚举
     */
    private enum AttendanceProcessType {
        ABSENT,         // 缺勤
        NO_CHECK_OUT    // 忘记签退
    }

    /**
     * 考勤处理结果
     */
    private static class AttendanceProcessResult {
        private final boolean processed;
        private final AttendanceProcessType type;

        private AttendanceProcessResult(boolean processed, AttendanceProcessType type) {
            this.processed = processed;
            this.type = type;
        }

        public static AttendanceProcessResult processed(AttendanceProcessType type) {
            return new AttendanceProcessResult(true, type);
        }

        public static AttendanceProcessResult notProcessed() {
            return new AttendanceProcessResult(false, null);
        }

        public boolean isProcessed() {
            return processed;
        }

        public AttendanceProcessType getType() {
            return type;
        }
    }

    public List<Attendance> getAttendancesByScheduleIds(List<Integer> scheduleIds) {
        if (scheduleIds == null || scheduleIds.isEmpty()) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<Attendance> attendanceLambdaQueryWrapper = new LambdaQueryWrapper<Attendance>()
                .in(Attendance::getScheduleId, scheduleIds)
                .eq(Attendance::getType, AttendanceEnum.NORMAL.getCode())  // 只查正常打卡记录
                .orderByDesc(Attendance::getCheckInTime);
        List<Attendance> attendances = attendanceMapper.selectList(attendanceLambdaQueryWrapper);
        return attendances;
    }
    @Scheduled(fixedRate = 300000)
    @Transactional
    public void checkLeavePostStatus() {
//        log.info("开始执行脱岗检测任务...");
        LocalDateTime now = LocalDateTime.now();
        LocalDate today = now.toLocalDate();
        Date currentDate = Date.from(now.atZone(ZoneId.systemDefault()).toInstant());
        //log.info("开始检查 {} 的排班记录", today);
        try {
            // 分页处理排班数据
            int pageNum = 1;
            boolean hasMore = true;
            while (hasMore) {
                //log.info("开始处理第 {} 页排班数据，日期：{}", pageNum, today);
                // 获取一页排班数据
                List<Schedule> schedules = scheduleService.getScheduleByDate(today, pageNum, BATCH_SIZE);
                
                if (schedules.isEmpty()) {
                    //log.info("没有找到需要检查的排班记录，日期：{}", today);
                    hasMore = false;
                    continue;
                }
                
                //log.info("本页找到 {} 条排班记录", schedules.size());
                
                // 获取所有用户ID
                List<Integer> userIds = schedules.stream()
                    .map(Schedule::getUserId)
                    .collect(Collectors.toList());
                
                // 查询这些用户中哪些正在请假
                LambdaQueryWrapper<Leave> leaveQuery = new LambdaQueryWrapper<>();
                leaveQuery.in(Leave::getUserId, userIds)
                          .eq(Leave::getStatus, LeaveStatusEnum.APPROVED.getCode())
                          .le(Leave::getStartTime, currentDate)
                          .ge(Leave::getEndTime, currentDate);
                
                List<Leave> leaves = leaveMapper.selectList(leaveQuery);
                
                // 将请假用户ID存入Set便于快速查找
                Set<Integer> leaveUserIds = leaves.stream()
                        .map(Leave::getUserId)
                        .collect(Collectors.toSet());
                
                // 获取这些排班的打卡记录
                List<Integer> scheduleIds = schedules.stream()
                    .map(Schedule::getId)
                    .collect(Collectors.toList());
                
                List<Attendance> attendances = getAttendancesByScheduleIds(scheduleIds);
                
                // 按排班ID分组的打卡记录
                Map<Integer, Attendance> scheduleAttendances = attendances.stream()
                    .collect(Collectors.toMap(
                        Attendance::getScheduleId,
                        a -> a,
                        (a1, a2) -> a1.getCheckInTime().isAfter(a2.getCheckInTime()) ? a1 : a2
                    ));
                
                // 处理每个排班
                for (Schedule schedule : schedules) {
                    // 如果用户正在请假，跳过脱岗检测
                    if (leaveUserIds.contains(schedule.getUserId())) {
                        //log.info("用户 {} 当前处于请假状态，跳过脱岗检测", schedule.getUserId());
                        continue;
                    }
                    
                    Shift shift = shiftService.getById(schedule.getShiftId());
                    if (shift == null) {
                        //log.info("未找到排班ID {} 对应的班次信息", schedule.getShiftId());
                        continue;
                    }
                    
                    LocalDateTime shiftStart = schedule.getScheduleDate().with(shift.getStartTime());
                    LocalDateTime shiftEnd = schedule.getScheduleDate().with(shift.getEndTime());
                    
                    // 如果当前时间在班次开始时间之前，跳过检查
                    if (now.isBefore(shiftStart)) {
                        //log.info("当前时间在班次开始之前，跳过检查");
                        continue;
                    }
                    
                    // 如果当前时间超过班次结束时间，检查是否有未结束的脱岗记录需要结束
                    if (now.isAfter(shiftEnd)) {
                        // 查询是否存在未结束的脱岗记录
                        LeavePost existingLeavePost = leavePostServiceImpl.getOne(new LambdaQueryWrapper<LeavePost>()
                                .eq(LeavePost::getUserId, schedule.getUserId())
                                .eq(LeavePost::getScheduleId, schedule.getId())
                                .eq(LeavePost::getStatus, 0)
                                .orderByDesc(LeavePost::getCreateTime)
                                .last("LIMIT 1"));
                                
                        if (existingLeavePost != null) {
                            // 结束脱岗记录，设置结束时间为班次结束时间
                            log.info("班次已结束，结束用户 {} 的脱岗记录", schedule.getUserId());
                            leavePostServiceImpl.endLeavePost(schedule.getUserId(), schedule.getId(), shiftEnd);
                        }
                        continue;
                    }
                    
                    // 获取该排班的打卡记录
                    QueryWrapper<Attendance> queryWrapper=new QueryWrapper<>();
                    queryWrapper.eq("schedule_id", schedule.getId());
                    Attendance attendance = attendanceMapper.selectOne(queryWrapper);

                    // 判断脱岗情况
                    boolean isLeavePost = false;
                    LocalDateTime leaveStartTime = null;
                    
                    if (attendance == null || attendance.getUpdateTime() == null) {
                        // 如果从未打卡或没有更新时间，立即标记为脱岗
                        isLeavePost = true;
                        leaveStartTime = shiftStart; // 脱岗开始时间设为班次开始时间
                    } else {
                        // 如果最后更新时间超过LEAVE_POST_THRESHOLD分钟
                        long minutesSinceLastUpdate = Duration.between(attendance.getUpdateTime(), now).toMinutes();
                        
                        if (minutesSinceLastUpdate >= relatedconfigurations.getOffDutyTime()) {
                            isLeavePost = true;
                            leaveStartTime = attendance.getUpdateTime().plusMinutes(relatedconfigurations.getOffDutyTime());
                        }
                    }
                    
                    if (isLeavePost) {
                        leavePostServiceImpl.startLeavePost(schedule.getUserId(), schedule.getId(), leaveStartTime);
                    } else {
                        // 检查是否存在未结束的脱岗记录
                        LeavePost existingLeavePost = leavePostServiceImpl.getOne(new LambdaQueryWrapper<LeavePost>()
                                .eq(LeavePost::getUserId, schedule.getUserId())
                                .eq(LeavePost::getScheduleId, schedule.getId())
                                .eq(LeavePost::getStatus, 0)  // 未结束状态
                                .orderByDesc(LeavePost::getCreateTime)
                                .last("LIMIT 1"));
                                
                        if (existingLeavePost != null) {
                            // 如果存在未结束的脱岗记录，则结束它
                            leavePostServiceImpl.endLeavePost(schedule.getUserId(), schedule.getId(), now);
                            log.info("用户 {} 已返回岗位，结束脱岗记录", schedule.getUserId());
                        } else {
                            log.info("用户 {} 的排班 {} 没有脱岗记录", schedule.getUserId(), schedule.getId());
                        }
                    }
                }
                
                // 检查是否还有更多数据
                if (schedules.size() < BATCH_SIZE) {
                    hasMore = false;
                } else {
                    pageNum++;
                }
            }
            
//            log.info("脱岗检测任务执行完成");
            
        } catch (Exception e) {
            log.error("脱岗检测任务执行异常", e);
        }
    }

    /**
     * 处理请假开始时的脱岗记录
     * 每5分钟执行一次
     */
    @Scheduled(fixedRate = 300000)
    @Transactional
    public void handleLeaveStartRecords() {
//        log.info("开始处理请假开始时的脱岗记录...");
        LocalDateTime now = LocalDateTime.now();
        Date currentDate = Date.from(now.atZone(ZoneId.systemDefault()).toInstant());
        
        try {
            // 查询刚刚开始的请假记录（前5分钟内开始的）
            LambdaQueryWrapper<Leave> leaveQuery = new LambdaQueryWrapper<>();
            leaveQuery.eq(Leave::getStatus, LeaveStatusEnum.APPROVED.getCode())
                      .le(Leave::getStartTime, currentDate)
                      .ge(Leave::getStartTime, Date.from(now.minusMinutes(5).atZone(ZoneId.systemDefault()).toInstant()));
            
            List<Leave> recentStartedLeaves = leaveMapper.selectList(leaveQuery);
            
            if (recentStartedLeaves.isEmpty()) {
                //log.info("没有找到刚刚开始的请假记录");
                return;
            }
            
            //log.info("找到 {} 条刚刚开始的请假记录", recentStartedLeaves.size());
            
            for (Leave leave : recentStartedLeaves) {
                //log.info("处理请假记录，ID: {}, 用户ID: {}, 开始时间: {}",
                //        leave.getId(), leave.getUserId(), leave.getStartTime());
                
                LocalDateTime startDateTime = leave.getStartTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
                LocalDateTime endDateTime = leave.getEndTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
                
                // 查询该时间段内的排班记录
                List<Schedule> schedules = scheduleMapper.getSchedulesByUserIdAndTimeRange(leave.getUserId(), startDateTime, endDateTime);
                //log.info("找到与请假时间重叠的排班记录: {} 条", schedules.size());
                
                for (Schedule schedule : schedules) {
                    //log.info("处理排班记录，ID: {}, 日期: {}", schedule.getId(), schedule.getScheduleDate());
                    
                    // 结束可能存在的脱岗记录
                    boolean ended = leavePostService.endLeavePostByScheduleId(schedule.getId(), now);
                    if (ended) {
                        log.info("已结束用户 {} 在请假开始时的脱岗记录，排班ID: {}", leave.getUserId(), schedule.getId());
                    } else {
                        log.warn("结束用户 {} 在请假开始时的脱岗记录失败，排班ID: {}", leave.getUserId(), schedule.getId());
                    }
                }
            }
            
//            log.info("处理请假开始时的脱岗记录完成");
            
        } catch (Exception e) {
            log.error("处理请假开始时的脱岗记录异常", e);
        }
    }

    /**
     * 每5分钟检查一次设备在线状态
     * 如果设备10分钟没有上报状态，则认为设备离线
     * 如果某个路口的两个摄像机都离线，则将对应的电脑也标记为离线
     * 
     * 状态记录优化：
     * - 只有在设备状态发生真正变化时才会记录新的状态历史
     * - 如果状态未变化，则保持之前的记录连续性（end_time为null）
     * - recordDeviceStatusChange方法已经智能处理状态变化检测
     */
    @Scheduled(fixedRate = 300000) // 每5分钟执行一次
    public void checkDeviceOnlineStatus() {
        log.info("开始执行设备在线状态检查任务");
        
        try {
            // 获取所有设备
            QueryWrapper<Device> queryWrapper = new QueryWrapper<>();
            List<Device> devices = deviceService.list(queryWrapper);
            // 按路口分组设备
            Map<String, List<Device>> devicesByLocation = devices.stream()
                .collect(Collectors.groupingBy(device -> 
                    String.format("%s|%s|%s|%s|%s", 
                        device.getCity(), 
                        device.getCounty(), 
                        device.getTownship(), 
                        device.getHamlet(), 
                        device.getSite()
                    )
                ));
            
            Date now = new Date();
            // 处理每个路口的设备
            for (List<Device> locationDevices : devicesByLocation.values()) {
                // 新增：为每台设备初始化历史记录（如无历史）
                for (Device device : locationDevices) {
                    boolean hasHistory = deviceService.hasDeviceStatusHistory(device.getEquipmentNumber(), device.getIp());
                    if (!hasHistory) {
                        deviceService.recordDeviceStatusChange(device, device.getState(), "系统初始化状态");
                    }
                }
                // 分离摄像机和电脑设备
                List<Device> cameras = locationDevices.stream()
                    .filter(d -> "摄像机".equals(d.getDeviceType()))
                    .collect(Collectors.toList());
                    
                List<Device> computers = locationDevices.stream()
                    .filter(d -> "电脑".equals(d.getDeviceType()))
                    .collect(Collectors.toList());
                
                // 检查摄像机状态
                int offlineCameraCount = 0;
                
                // 更新摄像机状态
                for (Device camera : cameras) {
                    Date lastOnlineTime = camera.getLastOnlineTime();
                    
                    // 检查摄像机是否离线
                    boolean shouldBeOffline = lastOnlineTime == null || 
                        (now.getTime() - lastOnlineTime.getTime()) > relatedconfigurations.getEquipmentOfflineTime()* 60 * 1000;
                    
                    // 更新摄像机状态
                    if (shouldBeOffline) {
                        if (camera.getState() != 1) {  // 如果不是离线状态，则状态发生变化
                            camera.setState(1);
                            deviceService.updateById(camera);
                            deviceService.recordDeviceStatusChange(camera, 1, "设备超时未上报状态");
                            log.info("摄像机{}已离线，最后在线时间：{}", camera.getEquipmentNumber(), lastOnlineTime);
                        }
                        // 移除持续离线状态的记录，因为recordDeviceStatusChange已经智能处理
                        offlineCameraCount++;  // 无论状态如何，只要应该离线就计数
                    } else {
                        if (camera.getState() != 0) {  // 如果不是在线状态，则状态发生变化
                            camera.setState(0);
                            deviceService.updateById(camera);
                            deviceService.recordDeviceStatusChange(camera, 0, "设备恢复在线");
                            log.info("摄像机{}恢复在线", camera.getEquipmentNumber());
                        }
                        // 移除持续在线状态的记录，因为recordDeviceStatusChange已经智能处理
                    }
                }
                
                // 更新电脑状态
                if (!computers.isEmpty() && cameras.size() > 0) {
                    // 修改判断逻辑：只要有一个摄像机在线，就认为电脑在线
                    boolean hasOnlineCamera = offlineCameraCount < cameras.size();
                    
                    for (Device computer : computers) {
                        // 检查电脑是否应该在线
                        boolean shouldBeOnline = hasOnlineCamera;
                        
                        // 如果状态需要改变
                        if (shouldBeOnline && computer.getState() != 0) {
                            // 电脑应该在线但当前不是在线状态
                            computer.setState(0);
                            deviceService.updateById(computer);
                            deviceService.recordDeviceStatusChange(computer, 0, "摄像机恢复在线导致电脑恢复在线");
                            log.info("由于存在在线摄像机，电脑{}已恢复在线", computer.getEquipmentNumber());
                        } else if (!shouldBeOnline && computer.getState() != 1) {
                            // 电脑应该离线但当前不是离线状态
                            computer.setState(1);
                            deviceService.updateById(computer);
                            deviceService.recordDeviceStatusChange(computer, 1, "所有摄像机离线导致电脑离线");
                            log.info("由于所有摄像机都离线，电脑{}已标记为离线", computer.getEquipmentNumber());
                        }
                    }
                }
            }
            
            log.info("设备在线状态检查任务执行完成");
        } catch (Exception e) {
            log.error("设备在线状态检查任务执行异常", e);
        }
    }

    /**
     * 定时任务：每天凌晨2点执行，核销超过30天的精准劝导记录
     * 将创建时间超过30天且未处理的AccuratePersuasion记录状态改为核销(已处理)
     * 同时将对应的IllegalRecords记录状态也改为核销(已处理)
     */
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
    @Transactional
    public void writeOffExpiredAccuratePersuasion() {
        log.info("开始执行精准劝导记录核销任务...");

        try {
            // 计算30天前的日期
            LocalDateTime thirtyDaysAgo = LocalDateTime.now().minusDays(30);
            Date thirtyDaysAgoDate = Date.from(thirtyDaysAgo.atZone(ZoneId.systemDefault()).toInstant());

            log.info("查找创建时间早于 {} 的未处理精准劝导记录", thirtyDaysAgo);

            // 查询创建时间超过30天且处理状态为未处理(0)的AccuratePersuasion记录
            QueryWrapper<AccuratePersuasion> queryWrapper = new QueryWrapper<>();
            queryWrapper.le("create_time", thirtyDaysAgoDate)  // 创建时间小于等于30天前
                       .eq("disposal_status", 0);              // 处理状态为未处理

            List<AccuratePersuasion> expiredRecords = accuratePersuasionMapper.selectList(queryWrapper);

            if (expiredRecords.isEmpty()) {
                log.info("没有找到需要核销的精准劝导记录");
                return;
            }

            log.info("找到 {} 条需要核销的精准劝导记录", expiredRecords.size());

            int successCount = 0;
            int errorCount = 0;

            // 处理每条记录
            for (AccuratePersuasion record : expiredRecords) {
                try {
                    // 更新AccuratePersuasion记录状态为已处理(1) - 表示核销
                    record.setDisposalStatus(3);
                    record.setProcessingTime(new Date()); // 设置处理时间为当前时间
                    record.setRemarks(record.getRemarks() == null ? "系统自动核销" : record.getRemarks() + " [系统自动核销]");

                    int updateResult = accuratePersuasionMapper.updateById(record);

                    if (updateResult > 0) {
                        // 更新对应的IllegalRecords记录状态为已处理(TREATED)
                        if (record.getIllegalRecordsUuid() != null) {
                            QueryWrapper<IllegalRecords> illegalQueryWrapper = new QueryWrapper<>();
                            illegalQueryWrapper.eq("UUID", record.getIllegalRecordsUuid());
                            IllegalRecords illegalRecord = new IllegalRecords();
                            illegalRecord.setDisposalStatus(HandleEnum.WRITE_OFF); // 设置为核销
                            int illegalUpdateResult = illegalRecordsMapper.update(illegalRecord, illegalQueryWrapper);
                            if (illegalUpdateResult > 0) {
                                successCount++;
                                log.warn("成功核销精准劝导记录: UUID={}, 违法记录UUID={}",
                                         record.getUuid(), record.getIllegalRecordsUuid());
                            } else {
                                log.warn("精准劝导记录核销成功，但违法记录更新失败: UUID={}, 违法记录UUID={}",
                                        record.getUuid(), record.getIllegalRecordsUuid());
                                errorCount++;
                            }
                        } else {
                            successCount++;
                            log.debug("成功核销精准劝导记录: UUID={} (无关联违法记录)", record.getUuid());
                        }
                    } else {
                        log.warn("精准劝导记录更新失败: UUID={}", record.getUuid());
                        errorCount++;
                    }

                } catch (Exception e) {
                    log.error("处理精准劝导记录时发生异常: UUID={}", record.getUuid(), e);
                    errorCount++;
                }
            }

            log.info("精准劝导记录核销任务执行完成 - 成功: {}, 失败: {}, 总计: {}",
                    successCount, errorCount, expiredRecords.size());

        } catch (Exception e) {
            log.error("精准劝导记录核销任务执行异常", e);
        }
    }

    /**
     * 定时任务：每天凌晨3点执行，清理90天前的录制文件
     * 删除物理文件和数据库记录，释放存储空间
     */
    @Scheduled(cron = "0 0 3 * * ?") // 每天凌晨3点执行
    @Transactional
    public void cleanupOldRecordingFiles() {
        log.info("开始执行录制文件清理任务...");

        try {
            // 计算90天前的时间点
            LocalDateTime cutoffTime = LocalDateTime.now().minusDays(90);
            log.info("清理截止时间: {}", cutoffTime);
            // 录制文件基础路径
            String recordingsBasePath = "/home/<USER>/live/";
            // 分批查询90天前的录制文件记录
            int pageSize = 100;
            int pageNum = 0;
            int totalProcessed = 0;
            int successCount = 0;
            int errorCount = 0;
            while (true) {
                // 查询一批过期的录制记录
                LambdaQueryWrapper<VideoRecording> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.lt(VideoRecording::getCreatedTime, cutoffTime)
                           .and(wrapper -> wrapper.isNull(VideoRecording::getStatus)
                                                 .or()
                                                 .ne(VideoRecording::getStatus, VideoRecording.STATUS_DELETED))
                           .last("LIMIT " + (pageNum * pageSize) + ", " + pageSize);

                List<VideoRecording> expiredRecordings = videoRecordingService.list(queryWrapper);

                if (expiredRecordings.isEmpty()) {
                    log.info("没有找到更多需要清理的录制文件");
                    break;
                }

                log.info("找到第{}批待清理的录制文件: {} 条", pageNum + 1, expiredRecordings.size());

                // 处理这批录制文件
                for (VideoRecording recording : expiredRecordings) {
                    try {

                        // 尝试删除物理文件
                        if (recording.getFilePath() != null && !recording.getFilePath().isEmpty()) {
                            Path filePath = Paths.get(recording.getFilePath());
                            if (Files.exists(filePath)) {
                                try {
                                    Files.delete(filePath);
                                    log.debug("成功删除录制文件: {}", recording.getFilePath());
                                } catch (Exception e) {
                                    log.warn("删除录制文件失败: {}, 错误: {}", recording.getFilePath(), e.getMessage());
                                }
                            } else {
                                // 文件不存在，认为已经被删除
                                log.debug("录制文件不存在，可能已被删除: {}", recording.getFilePath());
                            }
                        } else {
                            // 文件路径为空，直接处理数据库记录
                            log.debug("录制文件路径为空: ID={}", recording.getId());
                        }

                        // 更新数据库记录状态为已删除
                        recording.setStatus(VideoRecording.STATUS_DELETED);
                        recording.setUpdatedTime(LocalDateTime.now());
                        boolean dbUpdated = videoRecordingService.updateById(recording);

                        if (dbUpdated) {
                            successCount++;
                            log.debug("成功清理录制记录: ID={}, 文件={}, 创建时间={}",
                                    recording.getId(), recording.getFilePath(), recording.getCreatedTime());
                        } else {
                            log.warn("更新录制记录状态失败: ID={}", recording.getId());
                            errorCount++;
                        }

                    } catch (Exception e) {
                        log.error("处理录制文件时发生异常: ID={}, 文件={}",
                                recording.getId(), recording.getFilePath(), e);
                        errorCount++;
                    }

                    totalProcessed++;
                }

                pageNum++;

                // 避免无限循环，设置最大处理批次
                if (pageNum >= 1000) {
                    log.warn("已达到最大处理批次限制(1000批)，停止清理任务");
                    break;
                }
            }

            log.info("录制文件清理任务执行完成 - 总处理: {}, 成功: {}, 失败: {}",
                    totalProcessed, successCount, errorCount);

        } catch (Exception e) {
            log.error("录制文件清理任务执行异常", e);
        }
    }

    /**
     * 定时任务：每小时执行一次，处理违法数据的视频截取和合成
     * 查询前2小时到前1小时的违法数据，根据创建时间截取前后10秒的视频
     */
    @Scheduled(cron = "0 0 * * * ?") // 每小时执行一次
    @Transactional
    public void processIllegalVideoExtraction() {
        log.info("开始执行违法视频截取定时任务...");

        try {
            LocalDateTime now = LocalDateTime.now();
            // 查询前2小时到前1小时的数据
            LocalDateTime startTime = now.minusHours(2);
            LocalDateTime endTime = now.minusHours(1);

            log.info("查询时间范围: {} - {}", startTime, endTime);

            // 查询指定时间范围内的违法记录
            QueryWrapper<IllegalRecords> queryWrapper = new QueryWrapper<>();
            queryWrapper.between("create_time", startTime, endTime);
            queryWrapper.isNotNull("equipment_number");

            List<IllegalRecords> illegalRecords = illegalRecordsMapper.selectList(queryWrapper);

            if (illegalRecords.isEmpty()) {
                log.info("时间范围 {} - {} 内没有找到违法记录", startTime, endTime);
                return;
            }

            log.info("找到 {} 条违法记录需要处理", illegalRecords.size());

            int successCount = 0;
            int failCount = 0;

            // 遍历违法记录，处理视频截取
            for (IllegalRecords record : illegalRecords) {
                try {
                    // 转换创建时间为LocalDateTime
                    LocalDateTime createTime = record.getCreateTime().toInstant()
                            .atZone(ZoneId.systemDefault())
                            .toLocalDateTime();

                    log.info("处理违法记录: UUID={}, 创建时间={}, 设备编号={}",
                            record.getUuid(), createTime, record.getEquipmentNumber());

                    // 调用视频处理服务
                    cn.dev33.satoken.util.SaResult result = videoProcessingService.processIllegalVideo(
                            record.getUuid(),
                            createTime,
                            record.getEquipmentNumber()
                    );

                    if (result.getCode() == 200) {
                        successCount++;
                        log.info("违法记录 {} 视频处理成功: {}", record.getUuid(), result.getData());
                    } else {
                        failCount++;
                        log.warn("违法记录 {} 视频处理失败: {}", record.getUuid(), result.getMsg());
                    }

                } catch (Exception e) {
                    failCount++;
                    log.error("处理违法记录 {} 时发生异常", record.getUuid(), e);
                }
            }

            log.info("违法视频截取任务完成，成功: {}, 失败: {}", successCount, failCount);

        } catch (Exception e) {
            log.error("违法视频截取定时任务执行异常", e);
        }
    }

    /**
     * 定时任务：每分钟检查推流时间控制
     * 检查是否有推流超出允许时间段，记录日志并更新权限状态
     */
    @Scheduled(cron = "0 * * * * ?") // 每分钟执行一次
    public void checkStreamTimeControl() {
        try {
            // 获取超时的推流列表
            List<String> timeoutStreams = streamService.getTimeoutStreams();

            if (!timeoutStreams.isEmpty()) {
                log.info("发现 {} 个超时推流需要处理", timeoutStreams.size());

                for (String streamName : timeoutStreams) {
                    // 取消推流权限
                    streamService.setPublishPermission(streamName, false);
                    log.warn("推流超时控制 - 流: {} 已超出允许时间段且无观看者，已取消推流权限",
                            com.demo.controller.SrsCallbackController.SRS_MAP.get(streamName));
                }

                log.info("推流时间控制检查完成 - 处理了 {} 个超时推流", timeoutStreams.size());
            }

        } catch (Exception e) {
            log.error("推流时间控制检查任务执行异常", e);
        }
    }

}


