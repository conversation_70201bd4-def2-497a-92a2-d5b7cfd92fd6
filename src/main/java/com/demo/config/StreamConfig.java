package com.demo.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 推流控制配置
 * 用于管理推流权限和时间控制
 */
@Data
@Component
@ConfigurationProperties(prefix = "stream")
public class StreamConfig {
    
    /**
     * 推流总开关
     * true: 允许所有设备推流（不考虑观看者）
     * false: 按观看者逻辑控制推流权限
     */
    private Boolean tuiliu = false;
    
    /**
     * 允许推流的时间段配置
     */
    private AllowedTime allowedTime = new AllowedTime();
    
    @Data
    public static class AllowedTime {
        /**
         * 允许推流开始时间（24小时制，0-23）
         */
        private Integer start = 9;
        
        /**
         * 允许推流结束时间（24小时制，0-23）
         */
        private Integer end = 23;
    }
    
    /**
     * 检查当前时间是否在允许推流的时间段内
     * @return true表示在允许时间段内，false表示不在
     */
    public boolean isInAllowedTimeRange() {
        java.time.LocalTime now = java.time.LocalTime.now();
        int currentHour = now.getHour();
        
        int startHour = allowedTime.getStart();
        int endHour = allowedTime.getEnd();
        
        // 处理跨天的情况，如22-6点
        if (startHour <= endHour) {
            // 正常情况，如9-23点
            return currentHour >= startHour && currentHour <= endHour;
        } else {
            // 跨天情况，如22-6点
            return currentHour >= startHour || currentHour <= endHour;
        }
    }
}
